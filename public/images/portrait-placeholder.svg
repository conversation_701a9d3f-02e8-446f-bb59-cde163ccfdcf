<svg width="800" height="1200" viewBox="0 0 800 1200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Dark moody background -->
  <defs>
    <radialGradient id="moodGradient" cx="0.3" cy="0.2" r="1.2">
      <stop offset="0%" stop-color="#2a2a2a"/>
      <stop offset="50%" stop-color="#1a1a1a"/>
      <stop offset="100%" stop-color="#0a0a0a"/>
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="1200" fill="url(#moodGradient)"/>
  
  <!-- Subtle texture overlay -->
  <rect width="800" height="1200" fill="url(#noise)" opacity="0.1"/>
  
  <!-- Portrait silhouette -->
  <g transform="translate(200, 150)">
    <!-- Head shape -->
    <ellipse cx="200" cy="200" rx="120" ry="140" fill="#333" opacity="0.8"/>
    
    <!-- Shoulders -->
    <path d="M 80 320 Q 200 300 320 320 L 320 500 L 80 500 Z" fill="#333" opacity="0.8"/>
    
    <!-- Subtle highlight on face -->
    <ellipse cx="180" cy="180" rx="40" ry="60" fill="#444" opacity="0.6"/>
  </g>
  
  <!-- Atmospheric lighting effect -->
  <circle cx="250" cy="100" r="150" fill="url(#lightGradient)" opacity="0.1"/>
  
  <defs>
    <radialGradient id="lightGradient" cx="0.5" cy="0.5" r="0.8">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
    
    <!-- Noise pattern for texture -->
    <filter id="noise">
      <feTurbulence baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/>
      <feColorMatrix type="saturate" values="0"/>
    </filter>
  </defs>
</svg>
