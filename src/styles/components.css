/* Mobile-First Enhanced Section Styles */
section {
  padding: var(--space-3xl) 0;  /* Start with mobile padding */
  position: relative;
  overflow: hidden;
  color: var(--color-dark-grey);  /* Ensure readable text color by default */
}

/* Responsive section padding */
@media (min-width: 640px) {
  section {
    padding: var(--space-4xl) 0;
  }
}

@media (min-width: 1024px) {
  section {
    padding: var(--space-5xl) 0;
  }
}

section:nth-child(even) {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  color: var(--color-dark-grey);  /* Ensure dark text on light background */
}

section:nth-child(odd) {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  color: var(--color-dark-grey);  /* Ensure dark text on light background */
}

/* Add subtle section separators */
section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: 0.3;
}

section:first-child::before {
  display: none;
}

/* Section entrance animations */
section.revealed {
  animation: sectionReveal 0.8s var(--transition-smooth) forwards;
}

@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced About Section */
.about-section {
  position: relative;
}

.about-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: 0.2;
}

.about-section .about-content {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  padding: 0 var(--space-md);  /* Mobile padding */
}

/* Responsive about content */
@media (min-width: 640px) {
  .about-section .about-content {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .about-section .about-content {
    padding: 0;
  }
}

.about-section .about-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, var(--color-copper) 2px, transparent 2px);
  opacity: 0.3;
}

.about-section p {
  font-size: var(--text-base);  /* Start with mobile size */
  line-height: 1.7;
  margin-bottom: var(--space-lg);  /* Reduced for mobile */
  position: relative;
  padding: 0;  /* Remove padding, handled by container */
  color: var(--color-dark-grey);  /* Ensure readable text color */
}

.about-section p:first-child {
  font-size: var(--text-lg);  /* Smaller on mobile */
  font-weight: 500;
  color: var(--color-black);  /* Dark text for contrast */
  margin-bottom: var(--space-xl);
}

/* Responsive about text */
@media (min-width: 640px) {
  .about-section p {
    font-size: var(--text-lg);
    line-height: 1.8;
    margin-bottom: var(--space-xl);
  }

  .about-section p:first-child {
    font-size: var(--text-xl);
    margin-bottom: var(--space-2xl);
  }
}

.about-section p:last-child {
  margin-bottom: 0;
}

/* Add subtle text reveal animation */
.about-section p.revealed {
  animation: textReveal 0.8s var(--transition-smooth) forwards;
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Academic Journey Section */
.academic-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.academic-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.timeline {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  padding: var(--space-lg) var(--space-md);  /* Mobile padding */
}

/* Responsive timeline */
@media (min-width: 640px) {
  .timeline {
    padding: var(--space-xl) var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .timeline {
    padding: var(--space-xl) 0;
  }
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    var(--color-copper) 10%,
    var(--color-copper) 90%,
    transparent 100%
  );
  transform: translateX(-50%);
  box-shadow: 0 0 10px rgba(51, 80, 184, 0.3);
}

.timeline-item {
  display: flex;
  flex-direction: column;  /* Stack on mobile */
  margin-bottom: var(--space-2xl);  /* Reduced for mobile */
  position: relative;
  opacity: 0;
  transform: translateX(-30px);
  transition: all var(--duration-slow) var(--transition-smooth);
}

/* Responsive timeline items */
@media (min-width: 768px) {
  .timeline-item {
    flex-direction: row;
    margin-bottom: var(--space-4xl);
  }
}

.timeline-item.revealed {
  opacity: 1;
  transform: translateX(0);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 50%;
  top: var(--space-md);
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  border: 3px solid var(--color-white);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: 0 0 0 3px rgba(51, 80, 184, 0.2);
  transition: all var(--transition-normal);
}

.timeline-item:hover::before {
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 0 0 6px rgba(51, 80, 184, 0.3);
}

.timeline-date {
  flex: 1;
  text-align: left;  /* Left align on mobile */
  padding-right: 0;  /* No padding on mobile */
  padding-bottom: var(--space-sm);  /* Space below on mobile */
  font-weight: 600;
  color: var(--color-copper);
  font-size: var(--text-base);  /* Smaller on mobile */
  position: relative;
  transition: all var(--transition-normal);
}

/* Responsive timeline date */
@media (min-width: 768px) {
  .timeline-date {
    text-align: right;
    padding-right: var(--space-xl);
    padding-bottom: 0;
    font-size: var(--text-lg);
  }
}

.timeline-date::after {
  content: '';
  position: absolute;
  right: var(--space-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: var(--color-copper);
  transition: width var(--transition-normal);
}

.timeline-item:hover .timeline-date::after {
  width: 20px;
}

.timeline-content {
  flex: 1;
  padding-left: 0;  /* No left padding on mobile */
  background: var(--color-white);
  border-radius: 8px;  /* Smaller radius on mobile */
  padding: var(--space-md);  /* Smaller padding on mobile */
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  margin-top: var(--space-sm);  /* Space from date on mobile */
}

/* Responsive timeline content */
@media (min-width: 768px) {
  .timeline-content {
    padding-left: var(--space-xl);
    border-radius: 12px;
    padding: var(--space-lg);
    margin-top: 0;
  }
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: var(--space-lg);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--color-white);
}

.timeline-item:hover .timeline-content {
  transform: translateX(10px);
  box-shadow: var(--shadow-lg);
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);  /* Ensure dark text for contrast */
  position: relative;
}

.timeline-content h3::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-copper);
  transition: width var(--transition-normal);
}

.timeline-item:hover .timeline-content h3::after {
  width: 40px;
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-dark-grey);  /* Darker text for better readability */
  margin-bottom: 0;
  line-height: 1.6;
}

/* Academic Achievement Section */
.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid var(--color-copper);
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

/* Enhanced Research Interests Section */
.research-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.research-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 30% 40%, rgba(51, 80, 184, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(51, 80, 184, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.interests-list {
  max-width: 800px;
  margin: 0 auto;
  list-style: none;
  display: grid;
  gap: var(--space-md);  /* Smaller gap on mobile */
  padding: 0 var(--space-md);  /* Mobile padding */
}

/* Responsive interests list */
@media (min-width: 640px) {
  .interests-list {
    gap: var(--space-lg);
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .interests-list {
    padding: 0;
  }
}

.interest-item {
  padding: var(--space-lg);  /* Smaller padding on mobile */
  background: var(--color-white);
  border-radius: 8px;  /* Smaller radius on mobile */
  border-left: 3px solid var(--color-copper);  /* Thinner border on mobile */
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  color: var(--color-dark-grey);  /* Ensure readable text color */
}

/* Responsive interest items */
@media (min-width: 640px) {
  .interest-item {
    padding: var(--space-xl);
    border-radius: 12px;
    border-left-width: 4px;
  }
}

.interest-item.revealed {
  opacity: 1;
  transform: translateY(0);
}

.interest-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.02) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.interest-item:hover::before {
  opacity: 1;
}

.interest-item:hover {
  transform: translateX(12px) translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-left-color: var(--color-copper-dark);
}

.interest-item:last-child {
  margin-bottom: 0;
}

/* Add icon placeholder for future enhancement */
.interest-item::after {
  content: '→';
  position: absolute;
  right: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-xl);
  opacity: 0;
  transform: translateX(-10px);
  transition: all var(--transition-normal);
}

.interest-item:hover::after {
  opacity: 1;
  transform: translateX(0);
}

/* Enhanced Achievements Section */
.achievements-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.achievements-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.achievements-list {
  max-width: 900px;
  margin: 0 auto;
  list-style: none;
  display: grid;
  gap: var(--space-lg);  /* Smaller gap on mobile */
  padding: 0 var(--space-md);  /* Mobile padding */
}

/* Responsive achievements list */
@media (min-width: 640px) {
  .achievements-list {
    gap: var(--space-xl);
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .achievements-list {
    padding: 0;
  }
}

.achievement-item {
  padding: var(--space-lg);  /* Smaller padding on mobile */
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  border-radius: 12px;  /* Smaller radius on mobile */
  position: relative;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(51, 80, 184, 0.1);
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  overflow: hidden;
}

/* Responsive achievement items */
@media (min-width: 640px) {
  .achievement-item {
    padding: var(--space-xl);
    border-radius: 16px;
  }
}

.achievement-item.revealed {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.achievement-item::before {
  content: '★';
  position: absolute;
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-2xl);
  transition: all var(--transition-normal);
  z-index: 2;
}

.achievement-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.achievement-item:hover::after {
  opacity: 1;
}

.achievement-item {
  padding-left: var(--space-5xl);
}

.achievement-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
}

.achievement-item:hover::before {
  transform: scale(1.2) rotate(72deg);
  color: var(--color-copper-dark);
  text-shadow: 0 0 10px rgba(51, 80, 184, 0.5);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.achievement-item h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);  /* Ensure dark text for contrast */
  position: relative;
  z-index: 2;
}

.achievement-item p {
  color: var(--color-dark-grey);  /* Darker text for better readability */
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* Enhanced Affiliations Section */
.affiliations-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.affiliations-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 40% 30%, rgba(51, 80, 184, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 60% 70%, rgba(51, 80, 184, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-3xl);
  position: relative;
}

.affiliations-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-2xl);
  max-width: 700px;
  margin: 0 auto;
  align-items: center;
}

.logo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xl);
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  border: 1px solid rgba(51, 80, 184, 0.1);
}

.logo-item.revealed {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.logo-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.logo-item:hover::before {
  opacity: 1;
}

.logo-item:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
}

.institution-logo {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all var(--transition-normal);
  max-width: 100%;
  height: auto;
  position: relative;
  z-index: 2;
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

/* Future Aspirations Section */
.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  font-style: italic;
  position: relative;
}

.aspirations-quote::before {
  content: '"';
  font-size: 4rem;
  color: var(--color-copper);
  position: absolute;
  top: -1rem;
  left: -2rem;
  font-family: var(--font-serif);
}

.aspirations-quote p {
  font-size: var(--text-xl);
  line-height: 1.8;
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

/* Enhanced Contact Section */
.contact-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 50% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.contact-description {
  text-align: center;
  max-width: 700px;
  margin: 0 auto var(--space-2xl);  /* Reduced margin for mobile */
  color: var(--color-dark-grey);  /* Darker text for better readability */
  font-size: var(--text-base);  /* Smaller on mobile */
  line-height: 1.6;
  position: relative;
  padding: 0 var(--space-md);  /* Mobile padding */
}

/* Responsive contact description */
@media (min-width: 640px) {
  .contact-description {
    margin-bottom: var(--space-3xl);
    font-size: var(--text-lg);
    line-height: 1.7;
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .contact-description {
    padding: 0;
  }
}

.contact-description::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
}

.contact-info {
  text-align: center;
  position: relative;
}

.email-link {
  display: inline-block;
  font-size: var(--text-lg);  /* Smaller on mobile */
  font-weight: 500;
  padding: var(--space-md) var(--space-xl);  /* Better mobile touch target */
  min-height: var(--touch-target);  /* Ensure touch-friendly size */
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  border-radius: 25px;  /* Smaller radius on mobile */
  margin-bottom: var(--space-lg);  /* Reduced margin for mobile */
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive email link */
@media (min-width: 640px) {
  .email-link {
    font-size: var(--text-xl);
    padding: var(--space-lg) var(--space-2xl);
    border-radius: 50px;
    margin-bottom: var(--space-xl);
    display: inline-block;
  }
}

.email-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left var(--duration-slow);
}

.email-link:hover::before {
  left: 100%;
}

.email-link:hover {
  color: var(--color-white);
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, var(--color-copper-dark) 0%, var(--color-copper) 100%);
}

.social-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

/* Animation Enhancements */
.hero-content {
  animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: slideInFromLeft 1s ease-out 0.3s both;
}

.hero-title {
  animation: slideInFromLeft 1s ease-out 0.6s both;
}

.hero-tagline {
  animation: slideInFromLeft 1s ease-out 0.9s both;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Page Loading Animation */
@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

body {
  animation: pageLoad 0.6s ease-out;
}

/* Enhanced Section Title Animations */
.section-title {
  position: relative;
  overflow: hidden;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
  transition: width var(--duration-slow) var(--transition-smooth);
}

.section-title.revealed::before {
  width: 60px;
}

/* Smooth Focus States for Accessibility */
*:focus {
  outline: 2px solid var(--color-copper);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Loading Screen Styles */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-dark-grey) 0%, var(--color-black) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeOut 0.5s ease-out 2.5s forwards;
}

.loading-content {
  text-align: center;
  color: var(--color-white);
  position: relative;
  z-index: 2;
}

.loading-logo {
  margin-bottom: var(--space-2xl);
}

.logo-circle {
  width: 120px;
  height: 120px;
  border: 3px solid var(--color-copper);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  animation: rotate 2s linear infinite;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid transparent;
  border-top-color: var(--color-copper-light);
  border-radius: 50%;
  animation: rotate 1s linear infinite reverse;
}

.logo-inner {
  font-family: var(--font-hero);
  font-size: var(--text-3xl);
  font-weight: 300;
  color: var(--color-copper);
  animation: pulse 2s ease-in-out infinite;
}

.loading-text h2 {
  font-family: var(--font-hero);
  font-size: var(--text-2xl);
  margin-bottom: var(--space-sm);
  color: var(--color-white);
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

.loading-text p {
  font-size: var(--text-lg);
  color: var(--color-copper-light);
  margin-bottom: var(--space-2xl);
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.loading-progress {
  width: 300px;
  margin: 0 auto;
  animation: fadeInUp 0.8s ease-out 0.9s both;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(51, 80, 184, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-light));
  border-radius: 2px;
  transition: width 0.3s ease-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--color-copper-light);
  font-weight: 500;
}

.loading-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-copper);
  border-radius: 50%;
  opacity: 0.6;
}

/* Enhanced Print Styles */
@media print {
  .hero-section {
    height: auto;
    min-height: auto;
    page-break-inside: avoid;
  }

  .hero-overlay,
  .hero-background::before {
    display: none;
  }

  section {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .loading-screen {
    display: none;
  }
}

/* Mobile-specific enhancements */
@media (max-width: 767px) {
  /* Improve touch targets */
  .interest-item,
  .achievement-item,
  .logo-item {
    min-height: var(--touch-target);
  }

  /* Better text readability on mobile */
  .timeline-content h3 {
    font-size: var(--text-lg);
    line-height: 1.3;
  }

  .timeline-content p {
    font-size: var(--text-sm);
    line-height: 1.5;
  }

  .achievement-item h3 {
    font-size: var(--text-lg);
    line-height: 1.3;
  }

  .achievement-item p {
    font-size: var(--text-sm);
    line-height: 1.5;
  }

  /* Improve spacing for mobile reading */
  .interest-item,
  .achievement-item {
    line-height: 1.5;
  }

  /* Ensure proper spacing between elements */
  .hero-content > * + * {
    margin-top: var(--space-md);
  }

  /* Better mobile typography hierarchy */
  .section-title {
    line-height: 1.2;
    margin-bottom: var(--space-lg);
  }
}

/* Landscape mobile optimization */
@media (max-width: 767px) and (orientation: landscape) {
  .hero-section {
    min-height: 400px;
  }

  section {
    padding: var(--space-xl) 0;
  }
}

/* High DPI mobile screens */
@media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
  /* Ensure crisp text rendering on high DPI mobile screens */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Text Color Fixes for Proper Contrast */
/* Ensure all sections have proper text contrast */
.about-section,
.academic-section,
.research-section,
.achievements-section,
.affiliations-section,
.contact-section {
  color: var(--color-dark-grey);
}

/* Specific text elements that need dark text */
.about-section h2,
.about-section h3,
.about-section p,
.academic-section h2,
.academic-section h3,
.academic-section p,
.research-section h2,
.research-section h3,
.research-section p,
.achievements-section h2,
.achievements-section h3,
.achievements-section p,
.affiliations-section h2,
.affiliations-section h3,
.affiliations-section p,
.contact-section h2,
.contact-section h3,
.contact-section p {
  color: var(--color-dark-grey);
}

/* Section titles should be black for maximum contrast */
.section-title {
  color: var(--color-black) !important;
}

/* Timeline specific text colors */
.timeline-date {
  color: var(--color-copper) !important;
}

.timeline-content h3 {
  color: var(--color-black) !important;
}

.timeline-content p {
  color: var(--color-dark-grey) !important;
}

/* Interest items text */
.interest-item,
.interest-item h3,
.interest-item p {
  color: var(--color-dark-grey) !important;
}

/* Achievement items text */
.achievement-item h3 {
  color: var(--color-black) !important;
}

.achievement-item p {
  color: var(--color-dark-grey) !important;
}

/* Contact section text */
.contact-description {
  color: var(--color-dark-grey) !important;
}

/* Aspirations section (dark background) keeps white text */
.aspirations-section,
.aspirations-section h2,
.aspirations-section h3,
.aspirations-section p,
.aspirations-section .section-title {
  color: var(--color-white) !important;
}

/* List items text colors */
.interests-list li,
.achievements-list li {
  color: var(--color-dark-grey);
}

/* Ensure all headings in light sections are dark */
section:not(.aspirations-section) h1,
section:not(.aspirations-section) h2,
section:not(.aspirations-section) h3,
section:not(.aspirations-section) h4,
section:not(.aspirations-section) h5,
section:not(.aspirations-section) h6 {
  color: var(--color-black);
}

/* Ensure all paragraphs in light sections are dark grey */
section:not(.aspirations-section) p {
  color: var(--color-dark-grey);
}

/* Staggered animation for lists */
.interests-list .interest-item:nth-child(1) { animation-delay: 0.1s; }
.interests-list .interest-item:nth-child(2) { animation-delay: 0.2s; }
.interests-list .interest-item:nth-child(3) { animation-delay: 0.3s; }
.interests-list .interest-item:nth-child(4) { animation-delay: 0.4s; }
.interests-list .interest-item:nth-child(5) { animation-delay: 0.5s; }

.achievements-list .achievement-item:nth-child(1) { animation-delay: 0.1s; }
.achievements-list .achievement-item:nth-child(2) { animation-delay: 0.25s; }
.achievements-list .achievement-item:nth-child(3) { animation-delay: 0.4s; }
.achievements-list .achievement-item:nth-child(4) { animation-delay: 0.55s; }

/* Enhanced Mobile-First Responsive Styles */

/* Small tablets and large phones */
@media (min-width: 640px) {
  .logos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xl);
  }

  .loading-progress {
    width: 300px;
  }

  .logo-circle {
    width: 100px;
    height: 100px;
  }

  .logo-inner {
    font-size: var(--text-2xl);
  }
}

/* Tablets */
@media (min-width: 768px) {
  .timeline::before {
    left: 50%;
  }

  .timeline-item::before {
    left: 50%;
  }

  .timeline-date::after {
    right: var(--space-lg);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .logos-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-2xl);
  }

  .aspirations-quote {
    max-width: 900px;
  }

  .loading-progress {
    width: 300px;
  }

  .logo-circle {
    width: 120px;
    height: 120px;
  }

  .logo-inner {
    font-size: var(--text-3xl);
  }
}

/* Mobile-specific adjustments */
@media (max-width: 767px) {
  /* Timeline mobile layout */
  .timeline::before {
    left: var(--space-md);
  }

  .timeline-item::before {
    left: var(--space-md);
    transform: translateX(-50%);
  }

  .timeline-content::before {
    display: none;  /* Hide arrow on mobile */
  }

  /* Logos grid for mobile */
  .logos-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  /* Aspirations quote for mobile */
  .aspirations-quote::before {
    font-size: 2.5rem;
    top: -0.25rem;
    left: -0.5rem;
  }

  .aspirations-quote p {
    font-size: var(--text-base);
  }

  /* Social links for mobile */
  .social-links {
    flex-direction: column;
    gap: var(--space-sm);
  }

  /* Disable complex hover effects on mobile for performance */
  .timeline-item:hover {
    transform: none;
  }

  .timeline-item:hover .timeline-content {
    transform: none;
    box-shadow: var(--shadow-md);
  }

  .achievement-item:hover {
    transform: none;
    box-shadow: var(--shadow-md);
  }

  .interest-item:hover {
    transform: none;
    box-shadow: var(--shadow-md);
  }

  .logo-item:hover {
    transform: none;
  }

  /* Reduce animations for better mobile performance */
  .interest-item:hover::after {
    display: none;
  }

  .achievement-item:hover::before {
    transform: none;
  }
}

/* Small mobile devices */
@media (max-width: 479px) {
  /* Further reduce spacing for very small screens */
  section {
    padding: var(--space-2xl) 0;
  }

  /* Timeline adjustments for small screens */
  .timeline::before {
    left: var(--space-sm);
  }

  .timeline-item::before {
    left: var(--space-sm);
  }

  /* Achievement items for small screens */
  .achievement-item {
    padding-left: var(--space-3xl);
  }

  /* Loading screen adjustments */
  .loading-text h2 {
    font-size: var(--text-xl);
  }

  .loading-progress {
    width: 250px;
  }

  .logo-circle {
    width: 80px;
    height: 80px;
  }

  .logo-inner {
    font-size: var(--text-xl);
  }

  /* Ensure full width on very small screens */
  .about-section .about-content,
  .timeline,
  .interests-list,
  .achievements-list,
  .aspirations-quote {
    max-width: 100%;
  }
}
