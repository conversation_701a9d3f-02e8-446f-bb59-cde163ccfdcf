/* Section Styles */
section {
  padding: var(--space-5xl) 0;
}

section:nth-child(even) {
  background-color: #fafafa;
}

/* About Section */
.about-section .about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-section p {
  font-size: var(--text-lg);
  line-height: 1.8;
  margin-bottom: var(--space-xl);
}

.about-section p:last-child {
  margin-bottom: 0;
}

/* Academic Journey Section */
.academic-section {
  background-color: var(--color-white);
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--color-copper);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  margin-bottom: var(--space-3xl);
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 50%;
  top: var(--space-md);
  width: 12px;
  height: 12px;
  background: var(--color-copper);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-date {
  flex: 1;
  text-align: right;
  padding-right: var(--space-xl);
  font-weight: 600;
  color: var(--color-copper);
  font-size: var(--text-lg);
}

.timeline-content {
  flex: 1;
  padding-left: var(--space-xl);
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-medium-grey);
  margin-bottom: 0;
}

/* Academic Achievement Section */
.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid var(--color-copper);
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

/* Research Interests Section */
.research-section {
  background-color: #fafafa;
}

.interests-list {
  max-width: 700px;
  margin: 0 auto;
  list-style: none;
}

.interest-item {
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  background: var(--color-white);
  border-left: 4px solid var(--color-copper);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* transition: transform var(--transition-fast), box-shadow var(--transition-fast); */
}

.interest-item:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.interest-item:last-child {
  margin-bottom: 0;
}

/* Achievements Section */
.achievements-section {
  background-color: var(--color-white);
}

.achievements-list {
  max-width: 800px;
  margin: 0 auto;
  list-style: none;
}

.achievement-item {
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  position: relative;
  /* transition: transform var(--transition-fast); */
}

.achievement-item::before {
  content: '★';
  position: absolute;
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-xl);
}

.achievement-item {
  padding-left: var(--space-4xl);
}

.achievement-item:hover {
  transform: translateY(-2px);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

/* Affiliations Section */
.affiliations-section {
  background-color: #fafafa;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-2xl);
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-2xl);
  max-width: 600px;
  margin: 0 auto;
  align-items: center;
}

.logo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-lg);
  background: var(--color-white);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* transition: transform var(--transition-fast); */
}

.logo-item:hover {
  transform: scale(1.05);
}

.institution-logo {
  filter: grayscale(100%);
  opacity: 0.8;
  /* transition: filter var(--transition-fast), opacity var(--transition-fast); */
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
}

/* Future Aspirations Section */
.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  font-style: italic;
  position: relative;
}

.aspirations-quote::before {
  content: '"';
  font-size: 4rem;
  color: var(--color-copper);
  position: absolute;
  top: -1rem;
  left: -2rem;
  font-family: var(--font-serif);
}

.aspirations-quote p {
  font-size: var(--text-xl);
  line-height: 1.8;
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

/* Contact Section */
.contact-section {
  background-color: var(--color-white);
}

.contact-description {
  text-align: center;
  max-width: 600px;
  margin: 0 auto var(--space-2xl);
  color: var(--color-medium-grey);
}

.contact-info {
  text-align: center;
}

.email-link {
  display: inline-block;
  font-size: var(--text-xl);
  font-weight: 500;
  padding: var(--space-lg) var(--space-xl);
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  border-radius: 8px;
  margin-bottom: var(--space-xl);
  /* transition: transform var(--transition-fast), box-shadow var(--transition-fast); */
}

.email-link:hover {
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(184, 115, 51, 0.3);
}

.social-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

/* Animation Enhancements */
.hero-content {
  animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: slideInFromLeft 1s ease-out 0.3s both;
}

.hero-title {
  animation: slideInFromLeft 1s ease-out 0.6s both;
}

.hero-tagline {
  animation: slideInFromLeft 1s ease-out 0.9s both;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced hover effects */
.timeline-item {
  /* transition: all var(--transition-normal); */
}

.timeline-item:hover {
  transform: translateX(10px);
}

.timeline-item:hover .timeline-date {
  color: var(--color-copper-dark);
}

.achievement-item {
  /* transition: all var(--transition-normal); */
}

.achievement-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.achievement-item:hover::before {
  transform: scale(1.2);
  /* transition: transform var(--transition-fast); */
}

/* Staggered animation for lists */
.interests-list .interest-item:nth-child(1) { animation-delay: 0.1s; }
.interests-list .interest-item:nth-child(2) { animation-delay: 0.2s; }
.interests-list .interest-item:nth-child(3) { animation-delay: 0.3s; }
.interests-list .interest-item:nth-child(4) { animation-delay: 0.4s; }
.interests-list .interest-item:nth-child(5) { animation-delay: 0.5s; }

.achievements-list .achievement-item:nth-child(1) { animation-delay: 0.1s; }
.achievements-list .achievement-item:nth-child(2) { animation-delay: 0.25s; }
.achievements-list .achievement-item:nth-child(3) { animation-delay: 0.4s; }
.achievements-list .achievement-item:nth-child(4) { animation-delay: 0.55s; }

/* Tablet Responsive Styles */
@media (max-width: 1024px) {
  .timeline-item {
    margin-bottom: var(--space-2xl);
  }

  .logos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xl);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  section {
    padding: var(--space-3xl) 0;
  }

  /* Timeline adjustments for mobile */
  .timeline::before {
    left: var(--space-lg);
  }

  .timeline-item {
    flex-direction: column;
    padding-left: var(--space-2xl);
  }

  .timeline-item::before {
    left: var(--space-lg);
    transform: translateX(-50%);
  }

  .timeline-date {
    text-align: left;
    padding-right: 0;
    padding-bottom: var(--space-sm);
    font-size: var(--text-base);
  }

  .timeline-content {
    padding-left: 0;
  }

  /* Logos grid for mobile */
  .logos-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  /* Aspirations quote for mobile */
  .aspirations-quote::before {
    font-size: 3rem;
    top: -0.5rem;
    left: -1rem;
  }

  .aspirations-quote p {
    font-size: var(--text-lg);
  }

  /* Contact section for mobile */
  .email-link {
    font-size: var(--text-lg);
    padding: var(--space-md) var(--space-lg);
  }

  .social-links {
    flex-direction: column;
    gap: var(--space-sm);
  }

  /* Disable hover effects on mobile */
  .timeline-item:hover {
    transform: none;
  }

  .achievement-item:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* Small Mobile Responsive Styles */
@media (max-width: 480px) {
  section {
    padding: var(--space-2xl) 0;
  }

  .about-section .about-content {
    max-width: 100%;
  }

  .timeline {
    max-width: 100%;
  }

  .timeline-item {
    padding-left: var(--space-xl);
  }

  .timeline-item::before {
    left: var(--space-sm);
  }

  .timeline::before {
    left: var(--space-sm);
  }

  .interests-list {
    max-width: 100%;
  }

  .achievements-list {
    max-width: 100%;
  }

  .achievement-item {
    padding: var(--space-lg);
    padding-left: var(--space-3xl);
  }

  .aspirations-quote {
    max-width: 100%;
  }

  .aspirations-quote::before {
    font-size: 2.5rem;
    top: -0.25rem;
    left: -0.5rem;
  }

  .email-link {
    font-size: var(--text-base);
    padding: var(--space-sm) var(--space-md);
    display: block;
    text-align: center;
  }
}
