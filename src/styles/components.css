/* Enhanced Section Styles */
section {
  padding: var(--space-5xl) 0;
  position: relative;
  overflow: hidden;
}

section:nth-child(even) {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

section:nth-child(odd) {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
}

/* Add subtle section separators */
section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: 0.3;
}

section:first-child::before {
  display: none;
}

/* Section entrance animations */
section.revealed {
  animation: sectionReveal 0.8s var(--transition-smooth) forwards;
}

@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced About Section */
.about-section {
  position: relative;
}

.about-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: 0.2;
}

.about-section .about-content {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  position: relative;
}

.about-section .about-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, var(--color-copper) 2px, transparent 2px);
  opacity: 0.3;
}

.about-section p {
  font-size: var(--text-lg);
  line-height: 1.8;
  margin-bottom: var(--space-xl);
  position: relative;
  padding: 0 var(--space-lg);
}

.about-section p:first-child {
  font-size: var(--text-xl);
  font-weight: 500;
  color: var(--color-black);
  margin-bottom: var(--space-2xl);
}

.about-section p:last-child {
  margin-bottom: 0;
}

/* Add subtle text reveal animation */
.about-section p.revealed {
  animation: textReveal 0.8s var(--transition-smooth) forwards;
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Academic Journey Section */
.academic-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.academic-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.timeline {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  padding: var(--space-xl) 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    var(--color-copper) 10%,
    var(--color-copper) 90%,
    transparent 100%
  );
  transform: translateX(-50%);
  box-shadow: 0 0 10px rgba(51, 80, 184, 0.3);
}

.timeline-item {
  display: flex;
  margin-bottom: var(--space-4xl);
  position: relative;
  opacity: 0;
  transform: translateX(-30px);
  transition: all var(--duration-slow) var(--transition-smooth);
}

.timeline-item.revealed {
  opacity: 1;
  transform: translateX(0);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 50%;
  top: var(--space-md);
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  border: 3px solid var(--color-white);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: 0 0 0 3px rgba(51, 80, 184, 0.2);
  transition: all var(--transition-normal);
}

.timeline-item:hover::before {
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 0 0 6px rgba(51, 80, 184, 0.3);
}

.timeline-date {
  flex: 1;
  text-align: right;
  padding-right: var(--space-xl);
  font-weight: 600;
  color: var(--color-copper);
  font-size: var(--text-lg);
  position: relative;
  transition: all var(--transition-normal);
}

.timeline-date::after {
  content: '';
  position: absolute;
  right: var(--space-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: var(--color-copper);
  transition: width var(--transition-normal);
}

.timeline-item:hover .timeline-date::after {
  width: 20px;
}

.timeline-content {
  flex: 1;
  padding-left: var(--space-xl);
  background: var(--color-white);
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: var(--space-lg);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--color-white);
}

.timeline-item:hover .timeline-content {
  transform: translateX(10px);
  box-shadow: var(--shadow-lg);
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  position: relative;
}

.timeline-content h3::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-copper);
  transition: width var(--transition-normal);
}

.timeline-item:hover .timeline-content h3::after {
  width: 40px;
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  line-height: 1.6;
}

/* Academic Achievement Section */
.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid var(--color-copper);
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

/* Enhanced Research Interests Section */
.research-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.research-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 30% 40%, rgba(51, 80, 184, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(51, 80, 184, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.interests-list {
  max-width: 800px;
  margin: 0 auto;
  list-style: none;
  display: grid;
  gap: var(--space-lg);
}

.interest-item {
  padding: var(--space-xl);
  background: var(--color-white);
  border-radius: 12px;
  border-left: 4px solid var(--color-copper);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
}

.interest-item.revealed {
  opacity: 1;
  transform: translateY(0);
}

.interest-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.02) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.interest-item:hover::before {
  opacity: 1;
}

.interest-item:hover {
  transform: translateX(12px) translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-left-color: var(--color-copper-dark);
}

.interest-item:last-child {
  margin-bottom: 0;
}

/* Add icon placeholder for future enhancement */
.interest-item::after {
  content: '→';
  position: absolute;
  right: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-xl);
  opacity: 0;
  transform: translateX(-10px);
  transition: all var(--transition-normal);
}

.interest-item:hover::after {
  opacity: 1;
  transform: translateX(0);
}

/* Enhanced Achievements Section */
.achievements-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.achievements-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.achievements-list {
  max-width: 900px;
  margin: 0 auto;
  list-style: none;
  display: grid;
  gap: var(--space-xl);
}

.achievement-item {
  padding: var(--space-xl);
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  border-radius: 16px;
  position: relative;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(51, 80, 184, 0.1);
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  overflow: hidden;
}

.achievement-item.revealed {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.achievement-item::before {
  content: '★';
  position: absolute;
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-2xl);
  transition: all var(--transition-normal);
  z-index: 2;
}

.achievement-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.achievement-item:hover::after {
  opacity: 1;
}

.achievement-item {
  padding-left: var(--space-5xl);
}

.achievement-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
}

.achievement-item:hover::before {
  transform: scale(1.2) rotate(72deg);
  color: var(--color-copper-dark);
  text-shadow: 0 0 10px rgba(51, 80, 184, 0.5);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.achievement-item h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  position: relative;
  z-index: 2;
}

.achievement-item p {
  color: var(--color-medium-grey);
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* Enhanced Affiliations Section */
.affiliations-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.affiliations-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 40% 30%, rgba(51, 80, 184, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 60% 70%, rgba(51, 80, 184, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-3xl);
  position: relative;
}

.affiliations-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-2xl);
  max-width: 700px;
  margin: 0 auto;
  align-items: center;
}

.logo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xl);
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  border: 1px solid rgba(51, 80, 184, 0.1);
}

.logo-item.revealed {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.logo-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(51, 80, 184, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.logo-item:hover::before {
  opacity: 1;
}

.logo-item:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
}

.institution-logo {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all var(--transition-normal);
  max-width: 100%;
  height: auto;
  position: relative;
  z-index: 2;
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

/* Future Aspirations Section */
.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  font-style: italic;
  position: relative;
}

.aspirations-quote::before {
  content: '"';
  font-size: 4rem;
  color: var(--color-copper);
  position: absolute;
  top: -1rem;
  left: -2rem;
  font-family: var(--font-serif);
}

.aspirations-quote p {
  font-size: var(--text-xl);
  line-height: 1.8;
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

/* Enhanced Contact Section */
.contact-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 50% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.contact-description {
  text-align: center;
  max-width: 700px;
  margin: 0 auto var(--space-3xl);
  color: var(--color-medium-grey);
  font-size: var(--text-lg);
  line-height: 1.7;
  position: relative;
}

.contact-description::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
}

.contact-info {
  text-align: center;
  position: relative;
}

.email-link {
  display: inline-block;
  font-size: var(--text-xl);
  font-weight: 500;
  padding: var(--space-lg) var(--space-2xl);
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  border-radius: 50px;
  margin-bottom: var(--space-xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.email-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left var(--duration-slow);
}

.email-link:hover::before {
  left: 100%;
}

.email-link:hover {
  color: var(--color-white);
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, var(--color-copper-dark) 0%, var(--color-copper) 100%);
}

.social-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

/* Animation Enhancements */
.hero-content {
  animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: slideInFromLeft 1s ease-out 0.3s both;
}

.hero-title {
  animation: slideInFromLeft 1s ease-out 0.6s both;
}

.hero-tagline {
  animation: slideInFromLeft 1s ease-out 0.9s both;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Page Loading Animation */
@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

body {
  animation: pageLoad 0.6s ease-out;
}

/* Enhanced Section Title Animations */
.section-title {
  position: relative;
  overflow: hidden;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
  transition: width var(--duration-slow) var(--transition-smooth);
}

.section-title.revealed::before {
  width: 60px;
}

/* Smooth Focus States for Accessibility */
*:focus {
  outline: 2px solid var(--color-copper);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Enhanced Print Styles */
@media print {
  .hero-section {
    height: auto;
    min-height: auto;
    page-break-inside: avoid;
  }

  .hero-overlay,
  .hero-background::before {
    display: none;
  }

  section {
    page-break-inside: avoid;
    break-inside: avoid;
  }
}

/* Staggered animation for lists */
.interests-list .interest-item:nth-child(1) { animation-delay: 0.1s; }
.interests-list .interest-item:nth-child(2) { animation-delay: 0.2s; }
.interests-list .interest-item:nth-child(3) { animation-delay: 0.3s; }
.interests-list .interest-item:nth-child(4) { animation-delay: 0.4s; }
.interests-list .interest-item:nth-child(5) { animation-delay: 0.5s; }

.achievements-list .achievement-item:nth-child(1) { animation-delay: 0.1s; }
.achievements-list .achievement-item:nth-child(2) { animation-delay: 0.25s; }
.achievements-list .achievement-item:nth-child(3) { animation-delay: 0.4s; }
.achievements-list .achievement-item:nth-child(4) { animation-delay: 0.55s; }

/* Tablet Responsive Styles */
@media (max-width: 1024px) {
  .timeline-item {
    margin-bottom: var(--space-2xl);
  }

  .logos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xl);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  section {
    padding: var(--space-3xl) 0;
  }

  /* Timeline adjustments for mobile */
  .timeline::before {
    left: var(--space-lg);
  }

  .timeline-item {
    flex-direction: column;
    padding-left: var(--space-2xl);
  }

  .timeline-item::before {
    left: var(--space-lg);
    transform: translateX(-50%);
  }

  .timeline-date {
    text-align: left;
    padding-right: 0;
    padding-bottom: var(--space-sm);
    font-size: var(--text-base);
  }

  .timeline-content {
    padding-left: 0;
  }

  /* Logos grid for mobile */
  .logos-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  /* Aspirations quote for mobile */
  .aspirations-quote::before {
    font-size: 3rem;
    top: -0.5rem;
    left: -1rem;
  }

  .aspirations-quote p {
    font-size: var(--text-lg);
  }

  /* Contact section for mobile */
  .email-link {
    font-size: var(--text-lg);
    padding: var(--space-md) var(--space-lg);
  }

  .social-links {
    flex-direction: column;
    gap: var(--space-sm);
  }

  /* Disable hover effects on mobile */
  .timeline-item:hover {
    transform: none;
  }

  .achievement-item:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* Small Mobile Responsive Styles */
@media (max-width: 480px) {
  section {
    padding: var(--space-2xl) 0;
  }

  .about-section .about-content {
    max-width: 100%;
  }

  .timeline {
    max-width: 100%;
  }

  .timeline-item {
    padding-left: var(--space-xl);
  }

  .timeline-item::before {
    left: var(--space-sm);
  }

  .timeline::before {
    left: var(--space-sm);
  }

  .interests-list {
    max-width: 100%;
  }

  .achievements-list {
    max-width: 100%;
  }

  .achievement-item {
    padding: var(--space-lg);
    padding-left: var(--space-3xl);
  }

  .aspirations-quote {
    max-width: 100%;
  }

  .aspirations-quote::before {
    font-size: 2.5rem;
    top: -0.25rem;
    left: -0.5rem;
  }

  .email-link {
    font-size: var(--text-base);
    padding: var(--space-sm) var(--space-md);
    display: block;
    text-align: center;
  }
}
