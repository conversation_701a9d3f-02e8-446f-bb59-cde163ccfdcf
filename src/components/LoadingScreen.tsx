'use client';

import { useEffect, useState } from 'react';

export default function LoadingScreen() {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => setIsLoading(false), 500);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  if (!isLoading) return null;

  return (
    <div className="loading-screen">
      <div className="loading-content">
        <div className="loading-logo">
          <div className="logo-circle">
            <div className="logo-inner">WR</div>
          </div>
        </div>
        
        <div className="loading-text">
          <h2>Widmark Ramgoolie</h2>
          <p>Loading Portfolio...</p>
        </div>
        
        <div className="loading-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <span className="progress-text">{Math.round(progress)}%</span>
        </div>
      </div>
      
      <div className="loading-particles">
        {Array.from({ length: 20 }).map((_, i) => (
          <div key={i} className={`particle particle-${i}`} />
        ))}
      </div>
      
      <style jsx>{`
        .loading-screen {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--color-dark-grey) 0%, var(--color-black) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          animation: fadeOut 0.5s ease-out 0.5s forwards;
        }
        
        .loading-content {
          text-align: center;
          color: var(--color-white);
          position: relative;
          z-index: 2;
        }
        
        .loading-logo {
          margin-bottom: var(--space-2xl);
        }
        
        .logo-circle {
          width: 120px;
          height: 120px;
          border: 3px solid var(--color-copper);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          position: relative;
          animation: rotate 2s linear infinite;
        }
        
        .logo-circle::before {
          content: '';
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border: 3px solid transparent;
          border-top-color: var(--color-copper-light);
          border-radius: 50%;
          animation: rotate 1s linear infinite reverse;
        }
        
        .logo-inner {
          font-family: var(--font-hero);
          font-size: var(--text-3xl);
          font-weight: 300;
          color: var(--color-copper);
          animation: pulse 2s ease-in-out infinite;
        }
        
        .loading-text h2 {
          font-family: var(--font-hero);
          font-size: var(--text-2xl);
          margin-bottom: var(--space-sm);
          color: var(--color-white);
          animation: fadeInUp 0.8s ease-out 0.3s both;
        }
        
        .loading-text p {
          font-size: var(--text-lg);
          color: var(--color-copper-light);
          margin-bottom: var(--space-2xl);
          animation: fadeInUp 0.8s ease-out 0.6s both;
        }
        
        .loading-progress {
          width: 300px;
          margin: 0 auto;
          animation: fadeInUp 0.8s ease-out 0.9s both;
        }
        
        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(51, 80, 184, 0.2);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: var(--space-sm);
        }
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--color-copper), var(--color-copper-light));
          border-radius: 2px;
          transition: width 0.3s ease-out;
          position: relative;
        }
        
        .progress-fill::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          animation: shimmer 1.5s infinite;
        }
        
        .progress-text {
          font-size: var(--text-sm);
          color: var(--color-copper-light);
          font-weight: 500;
        }
        
        .loading-particles {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }
        
        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: var(--color-copper);
          border-radius: 50%;
          opacity: 0.6;
        }
        
        ${Array.from({ length: 20 }).map((_, i) => `
          .particle-${i} {
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
            animation-delay: ${Math.random() * 2}s;
          }
        `).join('')}
        
        @keyframes rotate {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes fadeInUp {
          0% {
            opacity: 0;
            transform: translateY(30px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes fadeOut {
          0% {
            opacity: 1;
            visibility: visible;
          }
          100% {
            opacity: 0;
            visibility: hidden;
          }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        @media (max-width: 768px) {
          .logo-circle {
            width: 80px;
            height: 80px;
          }
          
          .logo-inner {
            font-size: var(--text-xl);
          }
          
          .loading-text h2 {
            font-size: var(--text-xl);
          }
          
          .loading-progress {
            width: 250px;
          }
        }
      `}</style>
    </div>
  );
}
