export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "Widmark Ramgoolie",
    "jobTitle": "Medical Student",
    "description": "Fourth-year MBBS student at UWI exploring global medicine through local practice",
    "url": "https://widmarkramgoolie.com",
    "image": "https://widmarkramgoolie.com/images/portrait-placeholder.svg",
    "sameAs": [
      "https://linkedin.com/in/widmark-ramgoolie",
      "https://researchgate.net/profile/widmark-ramgoolie"
    ],
    "alumniOf": [
      {
        "@type": "EducationalOrganization",
        "name": "St. Joseph's Convent – St. Joseph"
      }
    ],
    "studiesAt": {
      "@type": "EducationalOrganization",
      "name": "University of the West Indies, St. Augustine",
      "department": "Faculty of Medical Sciences"
    },
    "knowsAbout": [
      "Medicine",
      "Healthcare Systems",
      "Medical Research", 
      "Community Health",
      "Health Equity",
      "Caribbean Healthcare",
      "Evidence-based Medicine"
    ],
    "award": [
      "Top Caribbean Performer in CAPE Biology (2020)",
      "Appointed Member, MSSC Auxiliary Executive (2024)",
      "Chairperson, Student Activities Committee – Faculty of Medical Sciences (2024)"
    ],
    "memberOf": [
      {
        "@type": "Organization",
        "name": "University of the West Indies"
      },
      {
        "@type": "Organization",
        "name": "MSSC Auxiliary Executive"
      }
    ],
    "nationality": "Trinidad and Tobago",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Trinidad and Tobago"
    },
    "email": "<EMAIL>"
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
