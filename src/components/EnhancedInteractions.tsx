'use client';

import { useEffect, useCallback } from 'react';

export default function EnhancedInteractions() {
  // Cursor trail effect for desktop
  const createCursorTrail = useCallback(() => {
    if (typeof window === 'undefined' || window.innerWidth < 768) return;

    const trail: HTMLElement[] = [];
    const trailLength = 8;

    // Create trail elements
    for (let i = 0; i < trailLength; i++) {
      const dot = document.createElement('div');
      dot.className = 'cursor-trail';
      dot.style.cssText = `
        position: fixed;
        width: ${6 - i * 0.5}px;
        height: ${6 - i * 0.5}px;
        background: rgba(51, 80, 184, ${0.8 - i * 0.1});
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transition: transform 0.1s ease-out;
        transform: translate(-50%, -50%);
      `;
      document.body.appendChild(dot);
      trail.push(dot);
    }

    let mouseX = 0;
    let mouseY = 0;
    let currentX = 0;
    let currentY = 0;

    const updateTrail = () => {
      currentX += (mouseX - currentX) * 0.1;
      currentY += (mouseY - currentY) * 0.1;

      trail.forEach((dot, index) => {
        const delay = index * 0.02;
        const x = currentX - (mouseX - currentX) * delay;
        const y = currentY - (mouseY - currentY) * delay;
        dot.style.left = `${x}px`;
        dot.style.top = `${y}px`;
      });

      requestAnimationFrame(updateTrail);
    };

    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    document.addEventListener('mousemove', handleMouseMove);
    updateTrail();

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      trail.forEach(dot => document.body.removeChild(dot));
    };
  }, []);

  // Enhanced scroll progress indicator
  const createScrollProgress = useCallback(() => {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    progressBar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 0%;
      height: 3px;
      background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
      z-index: 10000;
      transition: width 0.1s ease-out;
      box-shadow: 0 0 10px rgba(51, 80, 184, 0.5);
    `;
    document.body.appendChild(progressBar);

    const updateProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;
    };

    window.addEventListener('scroll', updateProgress, { passive: true });
    updateProgress();

    return () => {
      window.removeEventListener('scroll', updateProgress);
      if (progressBar.parentNode) {
        progressBar.parentNode.removeChild(progressBar);
      }
    };
  }, []);

  // Magnetic hover effect for interactive elements
  const createMagneticEffect = useCallback(() => {
    const magneticElements = document.querySelectorAll('.email-link, .logo-item, .achievement-item');

    magneticElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      
      const handleMouseMove = (e: MouseEvent) => {
        const rect = htmlElement.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        const distance = Math.sqrt(x * x + y * y);
        const maxDistance = 100;
        
        if (distance < maxDistance) {
          const strength = (maxDistance - distance) / maxDistance;
          const moveX = x * strength * 0.3;
          const moveY = y * strength * 0.3;
          
          htmlElement.style.transform = `translate(${moveX}px, ${moveY}px)`;
        }
      };

      const handleMouseLeave = () => {
        htmlElement.style.transform = '';
      };

      htmlElement.addEventListener('mousemove', handleMouseMove);
      htmlElement.addEventListener('mouseleave', handleMouseLeave);
    });
  }, []);

  // Typing animation for hero text
  const createTypingAnimation = useCallback(() => {
    const heroTagline = document.querySelector('.hero-tagline') as HTMLElement;
    if (!heroTagline) return;

    const text = heroTagline.textContent || '';
    heroTagline.textContent = '';
    heroTagline.style.borderRight = '2px solid var(--color-copper)';
    
    let index = 0;
    const typeSpeed = 100;
    
    const typeText = () => {
      if (index < text.length) {
        heroTagline.textContent += text.charAt(index);
        index++;
        setTimeout(typeText, typeSpeed);
      } else {
        // Remove cursor after typing is complete
        setTimeout(() => {
          heroTagline.style.borderRight = 'none';
        }, 1000);
      }
    };

    // Start typing after hero animations
    setTimeout(typeText, 2000);
  }, []);

  // Parallax effect for background elements
  const createParallaxElements = useCallback(() => {
    // Create floating geometric shapes
    const shapes = ['circle', 'triangle', 'square'];
    const shapeElements: HTMLElement[] = [];

    for (let i = 0; i < 6; i++) {
      const shape = document.createElement('div');
      const shapeType = shapes[Math.floor(Math.random() * shapes.length)];
      
      shape.className = `floating-shape floating-${shapeType}`;
      shape.style.cssText = `
        position: fixed;
        width: ${20 + Math.random() * 40}px;
        height: ${20 + Math.random() * 40}px;
        background: rgba(51, 80, 184, ${0.05 + Math.random() * 0.1});
        border-radius: ${shapeType === 'circle' ? '50%' : '0'};
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
        pointer-events: none;
        z-index: 1;
        animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
        animation-delay: ${Math.random() * 2}s;
      `;
      
      if (shapeType === 'triangle') {
        shape.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
      }
      
      document.body.appendChild(shape);
      shapeElements.push(shape);
    }

    const handleScroll = () => {
      const scrollY = window.pageYOffset;
      shapeElements.forEach((shape, index) => {
        const speed = 0.5 + (index % 3) * 0.2;
        shape.style.transform = `translateY(${scrollY * speed}px)`;
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      shapeElements.forEach(shape => {
        if (shape.parentNode) {
          shape.parentNode.removeChild(shape);
        }
      });
    };
  }, []);

  useEffect(() => {
    const cleanupFunctions: (() => void)[] = [];

    // Initialize all enhancements
    const cursorCleanup = createCursorTrail();
    const progressCleanup = createScrollProgress();
    
    // Delay these to ensure DOM is ready
    setTimeout(() => {
      createMagneticEffect();
      createTypingAnimation();
    }, 1000);

    const parallaxCleanup = createParallaxElements();

    if (cursorCleanup) cleanupFunctions.push(cursorCleanup);
    if (progressCleanup) cleanupFunctions.push(progressCleanup);
    if (parallaxCleanup) cleanupFunctions.push(parallaxCleanup);

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [createCursorTrail, createScrollProgress, createMagneticEffect, createTypingAnimation, createParallaxElements]);

  return null;
}
