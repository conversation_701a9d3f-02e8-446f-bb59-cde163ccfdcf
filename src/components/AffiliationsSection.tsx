import Image from 'next/image';

export default function AffiliationsSection() {
  const affiliations = [
    {
      name: "University of the West Indies",
      logo: "/images/Coat_of_arms_of_the_University_of_the_West_Indies.png",
      alt: "UWI Logo"
    },
    {
      name: "St. Joseph's Convent – St. Joseph",
      logo: "/images/301792561_567682651723271_2581192033815023727_n.jpg",
      alt: "St. Joseph's Convent Logo"
    }
  ];

  return (
    <section className="affiliations-section">
      <div className="container">
        <h2 className="section-title">Affiliations & Institutions</h2>
        <p className="affiliations-subtitle">Affiliated With:</p>
        <div className="logos-grid">
          {affiliations.map((affiliation, index) => (
            <div key={index} className="logo-item">
              <Image
                src={affiliation.logo}
                alt={affiliation.alt}
                width={160}
                height={200}
                className="institution-logo"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
