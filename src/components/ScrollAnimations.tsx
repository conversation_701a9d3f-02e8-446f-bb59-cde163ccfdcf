'use client';

import { useEffect, useCallback } from 'react';

export default function ScrollAnimations() {
  // Performance-optimized observer callback
  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {
    entries.forEach((entry) => {
      const element = entry.target as HTMLElement;
      const intersectionRatio = entry.intersectionRatio;

      if (entry.isIntersecting) {
        // Add visible class for basic animations
        element.classList.add('visible', 'revealed');

        // Add progressive reveal based on intersection ratio
        if (intersectionRatio > 0.5) {
          element.classList.add('fully-visible');
        }

        // Trigger staggered animations for child elements
        const children = element.querySelectorAll('.stagger-child');
        children.forEach((child, index) => {
          setTimeout(() => {
            (child as HTMLElement).classList.add('revealed');
          }, index * 100);
        });
      } else {
        // Optional: Remove classes for re-animation on scroll up
        if (intersectionRatio === 0) {
          element.classList.remove('fully-visible');
        }
      }
    });
  }, []);

  useEffect(() => {
    // Enhanced Intersection Observer for sophisticated animations
    const observerOptions = {
      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Enhanced section animations with different reveal types
    const sections = document.querySelectorAll('section');
    sections.forEach((section, index) => {
      const htmlSection = section as HTMLElement;

      // Alternate animation types for visual variety
      const animationType = index % 3;
      switch (animationType) {
        case 0:
          htmlSection.classList.add('reveal');
          break;
        case 1:
          htmlSection.classList.add('reveal-scale');
          break;
        case 2:
          htmlSection.classList.add('reveal-slide-left');
          break;
      }

      htmlSection.style.transitionDelay = `${index * 0.1}s`;
      observer.observe(section);
    });

    // Enhanced timeline items with staggered reveals
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
      const htmlItem = item as HTMLElement;
      htmlItem.classList.add('reveal-slide-right');
      htmlItem.style.transitionDelay = `${index * 0.2}s`;

      // Add stagger-child class to timeline content
      const content = htmlItem.querySelector('.timeline-content');
      if (content) {
        content.classList.add('stagger-child');
      }

      observer.observe(item);
    });

    // Enhanced interest items with hover effects
    const interestItems = document.querySelectorAll('.interest-item');
    interestItems.forEach((item, index) => {
      const htmlItem = item as HTMLElement;
      htmlItem.classList.add('reveal', 'hover-lift');
      htmlItem.style.transitionDelay = `${index * 0.1}s`;
      observer.observe(item);
    });

    // Enhanced achievement items with scale animations
    const achievementItems = document.querySelectorAll('.achievement-item');
    achievementItems.forEach((item, index) => {
      const htmlItem = item as HTMLElement;
      htmlItem.classList.add('reveal-scale', 'hover-lift');
      htmlItem.style.transitionDelay = `${index * 0.15}s`;
      observer.observe(item);
    });

    // Enhanced logo items with glow effects
    const logoItems = document.querySelectorAll('.logo-item');
    logoItems.forEach((item, index) => {
      const htmlItem = item as HTMLElement;
      htmlItem.classList.add('reveal-scale', 'hover-glow');
      htmlItem.style.transitionDelay = `${index * 0.2}s`;
      observer.observe(item);
    });

    // Smooth scroll for any anchor links (future use)
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.hash) {
        e.preventDefault();
        const element = document.querySelector(target.hash);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // Add smooth scroll to all anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
      link.addEventListener('click', handleSmoothScroll);
    });

    // Enhanced parallax effect with multiple layers
    let ticking = false;
    const handleParallax = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrolled = window.pageYOffset;
          const heroSection = document.querySelector('.hero-section') as HTMLElement;
          const heroImage = document.querySelector('.hero-image') as HTMLElement;
          const heroOverlay = document.querySelector('.hero-overlay') as HTMLElement;

          if (heroSection && heroImage && scrolled < window.innerHeight) {
            // Reduced parallax for hero image (faster, more responsive)
            const imageRate = scrolled * 0.15;  // Reduced from 0.3
            heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.05)`;  // Reduced scale

            // Minimal parallax for overlay
            if (heroOverlay) {
              const overlayRate = scrolled * 0.05;  // Reduced from 0.1
              heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;
            }

            // Faster fade out hero content as user scrolls
            const heroContent = document.querySelector('.hero-content') as HTMLElement;
            if (heroContent) {
              const fadeRate = Math.max(0, 1 - (scrolled / (window.innerHeight * 0.5)));  // Faster fade
              heroContent.style.opacity = fadeRate.toString();
              heroContent.style.transform = `translateY(${scrolled * 0.1}px)`;  // Reduced movement
            }
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add optimized parallax scroll listener
    window.addEventListener('scroll', handleParallax, { passive: true });

    // Cleanup function
    return () => {
      observer.disconnect();
      anchorLinks.forEach(link => {
        link.removeEventListener('click', handleSmoothScroll);
      });
      window.removeEventListener('scroll', handleParallax);
    };
  }, [observerCallback]);

  return null; // This component doesn't render anything
}
