import type { Metadata } from "next";
import "./globals.css";
import "../styles/components.css";
import StructuredData from "@/components/StructuredData";

export const metadata: Metadata = {
  metadataBase: new URL('https://widmarkramgoolie.com'),
  title: "Widmark Ramgoolie - Medical Student & Researcher",
  description: "Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.",
  keywords: "Widmark Ramgoolie, MBBS, medical student, UWI, Caribbean healthcare, medical research, Trinidad, St. Augustine",
  authors: [{ name: "Widmark Ramgoolie" }],
  creator: "Widmark Ramgoolie",
  publisher: "Widmark Ramgoolie",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://widmarkramgoolie.com",
    title: "Widmark Ramgoolie - Medical Student & Researcher",
    description: "Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.",
    siteName: "Widmark Ramgoolie Portfolio",
    images: [
      {
        url: "/images/portrait-placeholder.svg",
        width: 800,
        height: 1200,
        alt: "Widmark Ramgoolie Portrait",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Widmark Ramgoolie - Medical Student & Researcher",
    description: "Fourth-year MBBS student at UWI exploring global medicine through local practice.",
    images: ["/images/portrait-placeholder.svg"],
  },
  alternates: {
    canonical: "https://widmarkramgoolie.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&display=swap"
          rel="stylesheet"
        />
        <StructuredData />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
