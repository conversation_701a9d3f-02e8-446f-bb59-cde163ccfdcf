/* CSS Custom Properties */
:root {
  /* Colors - Greyscale with copper accent */
  --color-black: #0a0a0a;
  --color-dark-grey: #0a1322;
  --color-medium-grey: #4a4a4a;
  --color-light-grey: #8a8a8a;
  --color-very-light-grey: #e5e5e5;
  --color-white: #ffffff;
  --color-copper: #3350b8;
  --color-copper-light: #749ed4;
  --color-copper-dark: #2b458b;

  /* Typography */
  --font-serif: 'Playfair Display', Georgia, serif;
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-hero: 'Old Standard TT', Georgia, serif;

  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  --text-8xl: 8.75rem;   /* 60px */

  /* Spacing */
  --space-xs: 0.5rem;    /* 8px */
  --space-sm: 0.75rem;   /* 12px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  --space-4xl: 6rem;     /* 96px */
  --space-5xl: 8rem;     /* 128px */

  /* Layout */
  --container-max-width: 1200px;
  --container-padding: var(--space-lg);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-dark-grey);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 600;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-lg);
  font-size: var(--text-lg);
  line-height: 1.7;
}

a {
  color: var(--color-copper);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-copper-dark);
}

/* Layout Components */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: 600;
  margin-bottom: var(--space-2xl);
  text-align: center;
  color: var(--color-black);
}

/* Utility Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--transition-slow), transform var(--transition-slow);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-image {
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #000000b2 0%, #15296080 50%, #000c 100%);
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  text-align: center;
  color: var(--color-white);
  max-width: 800px;
  padding: 0 var(--space-lg);
}

.hero-name {
  font-family: var(--font-hero);
  font-size: var(--text-8xl);
  font-weight: 100;
  margin-bottom: var(--space-md);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: var(--color-white);
  font-style: italic;
  letter-spacing: -9%;
}

.hero-title {
  font-size: var(--text-2xl);
  font-weight: 400;
  font-family: var(--font-sans);
  margin-bottom: var(--space-lg);
  color: var(--color-copper-light);
}

.hero-tagline {
  font-size: var(--text-xl);
  font-weight: 300;
  font-style: italic;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Responsive Typography */
@media (max-width: 1200px) {
  :root {
    --container-padding: var(--space-lg);
  }
}

@media (max-width: 768px) {
  :root {
    --text-5xl: 2.5rem;   /* 40px */
    --text-4xl: 2rem;     /* 32px */
    --text-3xl: 1.5rem;   /* 24px */
    --text-6xl: 3rem;     /* 48px */
    --container-padding: var(--space-md);
    --space-5xl: 4rem;    /* Reduce section padding on mobile */
    --space-4xl: 3rem;
    --space-3xl: 2rem;
  }

  h1 {
    font-size: var(--text-4xl);
  }

  h2 {
    font-size: var(--text-3xl);
  }

  p {
    font-size: var(--text-base);
  }

  .hero-name {
    font-size: var(--text-5xl);
  }

  .hero-title {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 500px;
  }
}

@media (max-width: 480px) {
  :root {
    --text-6xl: 2.5rem;   /* 40px */
    --text-5xl: 2rem;     /* 32px */
    --text-4xl: 1.75rem;  /* 28px */
    --text-3xl: 1.25rem;  /* 20px */
    --container-padding: var(--space-sm);
    --space-5xl: 3rem;
    --space-4xl: 2rem;
    --space-3xl: 1.5rem;
  }

  .hero-name {
    font-size: var(--text-5xl);
    line-height: 1.1;
  }

  .hero-title {
    font-size: var(--text-lg);
  }

  .hero-tagline {
    font-size: var(--text-base);
  }

  .section-title {
    font-size: var(--text-3xl);
  }
}
