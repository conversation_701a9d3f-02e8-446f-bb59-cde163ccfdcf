/* CSS Custom Properties */
:root {
  /* Colors - Greyscale with copper accent */
  --color-black: #0a0a0a;
  --color-dark-grey: #0a1322;
  --color-medium-grey: #4a4a4a;
  --color-light-grey: #8a8a8a;
  --color-very-light-grey: #e5e5e5;
  --color-white: #ffffff;
  --color-copper: #3350b8;
  --color-copper-light: #749ed4;
  --color-copper-dark: #2b458b;

  /* Typography */
  --font-serif: 'Playfair Display', Georgia, serif;
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-hero: 'Old Standard TT', Georgia, serif;

  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  --text-8xl: 8.75rem;   /* 60px */

  /* Mobile-First Spacing System */
  --space-xs: 0.5rem;    /* 8px */
  --space-sm: 0.75rem;   /* 12px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 2.5rem;   /* 40px - reduced for mobile */
  --space-3xl: 3rem;     /* 48px - reduced for mobile */
  --space-4xl: 4rem;     /* 64px - reduced for mobile */
  --space-5xl: 5rem;     /* 80px - reduced for mobile */

  /* Touch-friendly sizes */
  --touch-target: 44px;  /* Minimum touch target size */
  --button-height: 48px; /* Standard button height */

  /* Mobile-First Layout System */
  --container-max-width: 1200px;
  --container-padding: var(--space-md);  /* Start with mobile padding */
  --container-padding-lg: var(--space-lg);
  --container-padding-xl: var(--space-xl);

  /* Responsive breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;

  /* Transitions & Animations */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Shadows & Depth */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(51, 80, 184, 0.3);

  /* Blur & Effects */
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);

  /* Animation Durations */
  --duration-fast: 200ms;
  --duration-normal: 400ms;
  --duration-slow: 600ms;
  --duration-slower: 800ms;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  overflow-x: hidden;
}

body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-dark-grey);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  position: relative;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-very-light-grey);
}

::-webkit-scrollbar-thumb {
  background: var(--color-copper);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-copper-dark);
}

/* Performance optimizations */
* {
  will-change: auto;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  /* Improve mobile scrolling performance */
  body {
    -webkit-overflow-scrolling: touch;
  }

  /* Optimize mobile interactions */
  button, a, .interest-item, .achievement-item, .logo-item {
    -webkit-tap-highlight-color: rgba(51, 80, 184, 0.2);
  }

  /* Prevent zoom on input focus (if we add forms later) */
  input, textarea, select {
    font-size: 16px;
  }
}

.hero-image,
.timeline-item,
.interest-item,
.achievement-item,
.logo-item {
  will-change: transform;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .hero-image {
    transform: none !important;
  }

  .floating-shape {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-copper: #0066cc;
    --color-copper-light: #3399ff;
    --color-copper-dark: #003d7a;
  }

  .hero-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* Remove problematic dark mode that's causing color issues */

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 600;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-lg);
  font-size: var(--text-lg);
  line-height: 1.7;
}

a {
  color: var(--color-copper);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-copper-dark);
}

/* Enhanced Mobile-First Layout Components */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  width: 100%;
}

/* Responsive container padding */
@media (min-width: 640px) {
  .container {
    padding: 0 var(--container-padding-lg);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--container-padding-xl);
  }
}

/* Content width utilities */
.content-narrow {
  max-width: 600px;
  margin: 0 auto;
}

.content-medium {
  max-width: 800px;
  margin: 0 auto;
}

.content-wide {
  max-width: 1000px;
  margin: 0 auto;
}

.section-title {
  font-size: var(--text-3xl);  /* Start smaller for mobile */
  font-weight: 600;
  margin-bottom: var(--space-xl);  /* Reduced spacing for mobile */
  text-align: center;
  color: var(--color-black);
  line-height: 1.2;
  padding: 0 var(--space-md);  /* Add padding for mobile */
}

/* Responsive section title sizing */
@media (min-width: 640px) {
  .section-title {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-2xl);
    padding: 0;
  }
}

/* Enhanced Utility Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--transition-slow), transform var(--transition-slow);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Advanced Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--transition-bounce) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Hover Enhancement Classes */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: transform var(--transition-bounce);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--color-very-light-grey) 0%,
    var(--color-white) 50%,
    var(--color-very-light-grey) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Intersection Observer Enhanced Classes */
.reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all var(--duration-slow) var(--transition-smooth);
}

.reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-scale {
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--duration-normal) var(--transition-bounce);
}

.reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

.reveal-slide-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all var(--duration-slow) var(--transition-smooth);
}

.reveal-slide-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-slide-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all var(--duration-slow) var(--transition-smooth);
}

.reveal-slide-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Advanced Keyframe Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-copper);
  }
  50% {
    box-shadow: 0 0 20px var(--color-copper), 0 0 30px var(--color-copper-light);
  }
}

/* Enhanced Hero Section Styles */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: var(--color-black);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  will-change: transform;
}

.hero-image {
  object-fit: cover;
  object-position: top;
  transition: transform var(--duration-slower) ease-out;
  will-change: transform;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(21, 41, 96, 0.5) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  z-index: 2;
}

/* Add subtle animated overlay patterns */
.hero-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 20% 80%,
    rgba(51, 80, 184, 0.1) 0%,
    transparent 50%
  ),
  radial-gradient(
    circle at 80% 20%,
    rgba(51, 80, 184, 0.1) 0%,
    transparent 50%
  );
  animation: float 6s ease-in-out infinite;
}

.hero-content {
  position: relative;
  z-index: 3;
  text-align: center;
  color: var(--color-white);
  max-width: 900px;
  padding: 0 var(--space-lg);
  animation: heroContentReveal 1.2s var(--transition-smooth) forwards;
}

@keyframes heroContentReveal {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.hero-name {
  font-family: var(--font-hero);
  font-size: var(--text-8xl);
  font-weight: 100;
  margin-bottom: var(--space-md);
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(51, 80, 184, 0.3);
  color: var(--color-white);
  font-style: italic;
  letter-spacing: -0.02em;
  opacity: 0;
  animation: heroNameReveal 1s var(--transition-smooth) 0.3s forwards;
  position: relative;
}

@keyframes heroNameReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
    filter: blur(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.hero-name::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  animation: underlineExpand 1s ease-out 1.3s forwards;
}

@keyframes underlineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 60%;
  }
}

.hero-title {
  font-size: var(--text-2xl);
  font-weight: 400;
  font-family: var(--font-sans);
  margin-bottom: var(--space-lg);
  color: var(--color-copper-light);
  opacity: 0;
  animation: heroTitleReveal 1s var(--transition-smooth) 0.6s forwards;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes heroTitleReveal {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-tagline {
  font-size: var(--text-xl);
  font-weight: 300;
  font-style: italic;
  opacity: 0;
  margin-bottom: 0;
  animation: heroTaglineReveal 1s var(--transition-smooth) 0.9s forwards;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes heroTaglineReveal {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 0.9;
    transform: translateX(0);
  }
}

/* Mobile-First Responsive Typography */
/* Small tablets and large phones */
@media (min-width: 640px) {
  :root {
    --text-5xl: 3rem;     /* 48px */
    --text-4xl: 2.25rem;  /* 36px */
    --text-3xl: 1.875rem; /* 30px */
    --space-2xl: 3rem;    /* 48px */
    --space-3xl: 4rem;    /* 64px */
    --space-4xl: 5rem;    /* 80px */
    --space-5xl: 6rem;    /* 96px */
  }

  .hero-name {
    font-size: var(--text-6xl);
  }

  .hero-title {
    font-size: var(--text-2xl);
  }

  .hero-tagline {
    font-size: var(--text-xl);
  }

  .hero-section {
    min-height: 600px;
  }
}

/* Tablets */
@media (min-width: 768px) {
  :root {
    --text-6xl: 3.75rem;  /* 60px */
    --text-5xl: 3rem;     /* 48px */
    --text-4xl: 2.25rem;  /* 36px */
    --space-3xl: 4rem;    /* 64px */
    --space-4xl: 6rem;    /* 96px */
    --space-5xl: 8rem;    /* 128px */
  }

  h1 {
    font-size: var(--text-5xl);
  }

  h2 {
    font-size: var(--text-4xl);
  }

  p {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 700px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  :root {
    --text-8xl: 6rem;     /* 96px - reduced from 140px for better mobile scaling */
    --text-6xl: 3.75rem;  /* 60px */
    --text-5xl: 3rem;     /* 48px */
    --space-4xl: 6rem;    /* 96px */
    --space-5xl: 8rem;    /* 128px */
  }

  .hero-name {
    font-size: var(--text-8xl);
  }

  .hero-section {
    min-height: 100vh;
  }
}

/* Large desktop */
@media (min-width: 1280px) {
  :root {
    --text-8xl: 8.75rem;  /* Original size for large screens */
  }
}

/* Mobile-specific adjustments */
@media (max-width: 639px) {
  .hero-name {
    font-size: var(--text-5xl);
    line-height: 1.1;
    letter-spacing: -0.01em;
  }

  .hero-title {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 500px;
    padding: var(--space-lg) 0;
  }

  .hero-content {
    padding: 0 var(--space-lg);
  }
}
