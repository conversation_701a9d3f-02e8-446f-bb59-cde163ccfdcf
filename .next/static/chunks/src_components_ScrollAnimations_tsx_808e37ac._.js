(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ScrollAnimations.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ScrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function ScrollAnimations() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ScrollAnimations.useEffect": ()=>{
            // Intersection Observer for fade-in animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            const observer = new IntersectionObserver({
                "ScrollAnimations.useEffect": (entries)=>{
                    entries.forEach({
                        "ScrollAnimations.useEffect": (entry)=>{
                            if (entry.isIntersecting) {
                                entry.target.classList.add('visible');
                            }
                        }
                    }["ScrollAnimations.useEffect"]);
                }
            }["ScrollAnimations.useEffect"], observerOptions);
            // Add fade-in class to all sections and observe them
            const sections = document.querySelectorAll('section');
            sections.forEach({
                "ScrollAnimations.useEffect": (section, index)=>{
                    section.classList.add('fade-in');
                    section.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(section);
                }
            }["ScrollAnimations.useEffect"]);
            // Add fade-in to timeline items
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    item.classList.add('fade-in');
                    item.style.transitionDelay = `${index * 0.2}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Add fade-in to interest items
            const interestItems = document.querySelectorAll('.interest-item');
            interestItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    item.classList.add('fade-in');
                    item.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Add fade-in to achievement items
            const achievementItems = document.querySelectorAll('.achievement-item');
            achievementItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    item.classList.add('fade-in');
                    item.style.transitionDelay = `${index * 0.15}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Add fade-in to logo items
            const logoItems = document.querySelectorAll('.logo-item');
            logoItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    item.classList.add('fade-in');
                    item.style.transitionDelay = `${index * 0.2}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Smooth scroll for any anchor links (future use)
            const handleSmoothScroll = {
                "ScrollAnimations.useEffect.handleSmoothScroll": (e)=>{
                    const target = e.target;
                    if (target.hash) {
                        e.preventDefault();
                        const element = document.querySelector(target.hash);
                        if (element) {
                            element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                }
            }["ScrollAnimations.useEffect.handleSmoothScroll"];
            // Add smooth scroll to all anchor links
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach({
                "ScrollAnimations.useEffect": (link)=>{
                    link.addEventListener('click', handleSmoothScroll);
                }
            }["ScrollAnimations.useEffect"]);
            // Optimized parallax effect for hero section
            let ticking = false;
            const handleParallax = {
                "ScrollAnimations.useEffect.handleParallax": ()=>{
                    if (!ticking) {
                        requestAnimationFrame({
                            "ScrollAnimations.useEffect.handleParallax": ()=>{
                                const scrolled = window.pageYOffset;
                                const heroSection = document.querySelector('.hero-section');
                                const heroImage = document.querySelector('.hero-image');
                                if (heroSection && heroImage && scrolled < window.innerHeight) {
                                    const rate = scrolled * 0.3;
                                    heroImage.style.transform = `translate3d(0, ${rate}px, 0)`;
                                }
                                ticking = false;
                            }
                        }["ScrollAnimations.useEffect.handleParallax"]);
                        ticking = true;
                    }
                }
            }["ScrollAnimations.useEffect.handleParallax"];
            // Add optimized parallax scroll listener
            window.addEventListener('scroll', handleParallax, {
                passive: true
            });
            // Cleanup function
            return ({
                "ScrollAnimations.useEffect": ()=>{
                    observer.disconnect();
                    anchorLinks.forEach({
                        "ScrollAnimations.useEffect": (link)=>{
                            link.removeEventListener('click', handleSmoothScroll);
                        }
                    }["ScrollAnimations.useEffect"]);
                    window.removeEventListener('scroll', handleParallax);
                }
            })["ScrollAnimations.useEffect"];
        }
    }["ScrollAnimations.useEffect"], []);
    return null; // This component doesn't render anything
}
_s(ScrollAnimations, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = ScrollAnimations;
var _c;
__turbopack_context__.k.register(_c, "ScrollAnimations");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ScrollAnimations_tsx_808e37ac._.js.map