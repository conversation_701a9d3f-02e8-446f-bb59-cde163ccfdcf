(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ScrollAnimations.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ScrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function ScrollAnimations() {
    _s();
    // Performance-optimized observer callback
    const observerCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ScrollAnimations.useCallback[observerCallback]": (entries)=>{
            entries.forEach({
                "ScrollAnimations.useCallback[observerCallback]": (entry)=>{
                    const element = entry.target;
                    const intersectionRatio = entry.intersectionRatio;
                    if (entry.isIntersecting) {
                        // Add visible class for basic animations
                        element.classList.add('visible', 'revealed');
                        // Add progressive reveal based on intersection ratio
                        if (intersectionRatio > 0.5) {
                            element.classList.add('fully-visible');
                        }
                        // Trigger staggered animations for child elements
                        const children = element.querySelectorAll('.stagger-child');
                        children.forEach({
                            "ScrollAnimations.useCallback[observerCallback]": (child, index)=>{
                                setTimeout({
                                    "ScrollAnimations.useCallback[observerCallback]": ()=>{
                                        child.classList.add('revealed');
                                    }
                                }["ScrollAnimations.useCallback[observerCallback]"], index * 100);
                            }
                        }["ScrollAnimations.useCallback[observerCallback]"]);
                    } else {
                        // Optional: Remove classes for re-animation on scroll up
                        if (intersectionRatio === 0) {
                            element.classList.remove('fully-visible');
                        }
                    }
                }
            }["ScrollAnimations.useCallback[observerCallback]"]);
        }
    }["ScrollAnimations.useCallback[observerCallback]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ScrollAnimations.useEffect": ()=>{
            // Enhanced Intersection Observer for sophisticated animations
            const observerOptions = {
                threshold: [
                    0,
                    0.1,
                    0.25,
                    0.5,
                    0.75,
                    1
                ],
                rootMargin: '0px 0px -50px 0px'
            };
            const observer = new IntersectionObserver(observerCallback, observerOptions);
            // Enhanced section animations with different reveal types
            const sections = document.querySelectorAll('section');
            sections.forEach({
                "ScrollAnimations.useEffect": (section, index)=>{
                    const htmlSection = section;
                    // Alternate animation types for visual variety
                    const animationType = index % 3;
                    switch(animationType){
                        case 0:
                            htmlSection.classList.add('reveal');
                            break;
                        case 1:
                            htmlSection.classList.add('reveal-scale');
                            break;
                        case 2:
                            htmlSection.classList.add('reveal-slide-left');
                            break;
                    }
                    htmlSection.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(section);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced timeline items with staggered reveals
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-slide-right');
                    htmlItem.style.transitionDelay = `${index * 0.2}s`;
                    // Add stagger-child class to timeline content
                    const content = htmlItem.querySelector('.timeline-content');
                    if (content) {
                        content.classList.add('stagger-child');
                    }
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced interest items with hover effects
            const interestItems = document.querySelectorAll('.interest-item');
            interestItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal', 'hover-lift');
                    htmlItem.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced achievement items with scale animations
            const achievementItems = document.querySelectorAll('.achievement-item');
            achievementItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-scale', 'hover-lift');
                    htmlItem.style.transitionDelay = `${index * 0.15}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced logo items with glow effects
            const logoItems = document.querySelectorAll('.logo-item');
            logoItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-scale', 'hover-glow');
                    htmlItem.style.transitionDelay = `${index * 0.2}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Smooth scroll for any anchor links (future use)
            const handleSmoothScroll = {
                "ScrollAnimations.useEffect.handleSmoothScroll": (e)=>{
                    const target = e.target;
                    if (target.hash) {
                        e.preventDefault();
                        const element = document.querySelector(target.hash);
                        if (element) {
                            element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                }
            }["ScrollAnimations.useEffect.handleSmoothScroll"];
            // Add smooth scroll to all anchor links
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach({
                "ScrollAnimations.useEffect": (link)=>{
                    link.addEventListener('click', handleSmoothScroll);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced parallax effect with multiple layers
            let ticking = false;
            const handleParallax = {
                "ScrollAnimations.useEffect.handleParallax": ()=>{
                    if (!ticking) {
                        requestAnimationFrame({
                            "ScrollAnimations.useEffect.handleParallax": ()=>{
                                const scrolled = window.pageYOffset;
                                const heroSection = document.querySelector('.hero-section');
                                const heroImage = document.querySelector('.hero-image');
                                const heroOverlay = document.querySelector('.hero-overlay');
                                if (heroSection && heroImage && scrolled < window.innerHeight) {
                                    // Parallax for hero image
                                    const imageRate = scrolled * 0.3;
                                    heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.1)`;
                                    // Subtle parallax for overlay
                                    if (heroOverlay) {
                                        const overlayRate = scrolled * 0.1;
                                        heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;
                                    }
                                    // Fade out hero content as user scrolls
                                    const heroContent = document.querySelector('.hero-content');
                                    if (heroContent) {
                                        const fadeRate = Math.max(0, 1 - scrolled / (window.innerHeight * 0.8));
                                        heroContent.style.opacity = fadeRate.toString();
                                        heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;
                                    }
                                }
                                ticking = false;
                            }
                        }["ScrollAnimations.useEffect.handleParallax"]);
                        ticking = true;
                    }
                }
            }["ScrollAnimations.useEffect.handleParallax"];
            // Add optimized parallax scroll listener
            window.addEventListener('scroll', handleParallax, {
                passive: true
            });
            // Cleanup function
            return ({
                "ScrollAnimations.useEffect": ()=>{
                    observer.disconnect();
                    anchorLinks.forEach({
                        "ScrollAnimations.useEffect": (link)=>{
                            link.removeEventListener('click', handleSmoothScroll);
                        }
                    }["ScrollAnimations.useEffect"]);
                    window.removeEventListener('scroll', handleParallax);
                }
            })["ScrollAnimations.useEffect"];
        }
    }["ScrollAnimations.useEffect"], [
        observerCallback
    ]);
    return null; // This component doesn't render anything
}
_s(ScrollAnimations, "itVl4HLVJritfmW2X9C+P71p/6s=");
_c = ScrollAnimations;
var _c;
__turbopack_context__.k.register(_c, "ScrollAnimations");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ScrollAnimations_tsx_808e37ac._.js.map