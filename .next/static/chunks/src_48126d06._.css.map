{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/globals.css"], "sourcesContent": ["/* CSS Custom Properties */\n:root {\n  /* Colors - Greyscale with copper accent */\n  --color-black: #0a0a0a;\n  --color-dark-grey: #0a1322;\n  --color-medium-grey: #4a4a4a;\n  --color-light-grey: #8a8a8a;\n  --color-very-light-grey: #e5e5e5;\n  --color-white: #ffffff;\n  --color-copper: #3350b8;\n  --color-copper-light: #749ed4;\n  --color-copper-dark: #2b458b;\n\n  /* Typography */\n  --font-serif: 'Playfair Display', Georgia, serif;\n  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  --font-hero: 'Old Standard TT', Georgia, serif;\n\n  /* Font Sizes */\n  --text-xs: 0.75rem;    /* 12px */\n  --text-sm: 0.875rem;   /* 14px */\n  --text-base: 1rem;     /* 16px */\n  --text-lg: 1.125rem;   /* 18px */\n  --text-xl: 1.25rem;    /* 20px */\n  --text-2xl: 1.5rem;    /* 24px */\n  --text-3xl: 1.875rem;  /* 30px */\n  --text-4xl: 2.25rem;   /* 36px */\n  --text-5xl: 3rem;      /* 48px */\n  --text-6xl: 3.75rem;   /* 60px */\n  --text-8xl: 8.75rem;   /* 60px */\n\n  /* Spacing */\n  --space-xs: 0.5rem;    /* 8px */\n  --space-sm: 0.75rem;   /* 12px */\n  --space-md: 1rem;      /* 16px */\n  --space-lg: 1.5rem;    /* 24px */\n  --space-xl: 2rem;      /* 32px */\n  --space-2xl: 3rem;     /* 48px */\n  --space-3xl: 4rem;     /* 64px */\n  --space-4xl: 6rem;     /* 96px */\n  --space-5xl: 8rem;     /* 128px */\n\n  /* Layout */\n  --container-max-width: 1200px;\n  --container-padding: var(--space-lg);\n\n  /* Transitions & Animations */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s ease;\n  --transition-slow: 0.5s ease;\n  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);\n  --transition-smooth: 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n\n  /* Shadows & Depth */\n  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);\n  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);\n  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);\n  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);\n  --shadow-glow: 0 0 20px rgba(51, 80, 184, 0.3);\n\n  /* Blur & Effects */\n  --blur-sm: blur(4px);\n  --blur-md: blur(8px);\n  --blur-lg: blur(16px);\n\n  /* Animation Durations */\n  --duration-fast: 200ms;\n  --duration-normal: 400ms;\n  --duration-slow: 600ms;\n  --duration-slower: 800ms;\n}\n\n/* Reset and Base Styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n  overflow-x: hidden;\n}\n\nbody {\n  font-family: var(--font-sans);\n  font-weight: 400;\n  line-height: 1.6;\n  color: var(--color-dark-grey);\n  background-color: var(--color-white);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow-x: hidden;\n  position: relative;\n}\n\n/* Enhanced scrollbar styling */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--color-very-light-grey);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--color-copper);\n  border-radius: 4px;\n  transition: background var(--transition-fast);\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--color-copper-dark);\n}\n\n/* Performance optimizations */\n* {\n  will-change: auto;\n}\n\n.hero-image,\n.timeline-item,\n.interest-item,\n.achievement-item,\n.logo-item {\n  will-change: transform;\n}\n\n/* Reduce motion for users who prefer it */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n\n  .hero-image {\n    transform: none !important;\n  }\n\n  .floating-shape {\n    animation: none !important;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  :root {\n    --color-copper: #0066cc;\n    --color-copper-light: #3399ff;\n    --color-copper-dark: #003d7a;\n  }\n\n  .hero-overlay {\n    background: rgba(0, 0, 0, 0.8);\n  }\n}\n\n/* Dark mode support (future enhancement) */\n@media (prefers-color-scheme: dark) {\n  :root {\n    --color-white: #1a1a1a;\n    --color-very-light-grey: #2a2a2a;\n    --color-light-grey: #4a4a4a;\n    --color-medium-grey: #8a8a8a;\n    --color-dark-grey: #e5e5e5;\n    --color-black: #ffffff;\n  }\n}\n\n/* Typography Styles */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-serif);\n  font-weight: 600;\n  line-height: 1.2;\n  margin-bottom: var(--space-md);\n  color: var(--color-black);\n}\n\nh1 {\n  font-size: var(--text-5xl);\n  font-weight: 700;\n}\n\nh2 {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n}\n\nh3 {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n}\n\nh4 {\n  font-size: var(--text-xl);\n  font-weight: 500;\n}\n\np {\n  margin-bottom: var(--space-lg);\n  font-size: var(--text-lg);\n  line-height: 1.7;\n}\n\na {\n  color: var(--color-copper);\n  text-decoration: none;\n  transition: color var(--transition-fast);\n}\n\na:hover {\n  color: var(--color-copper-dark);\n}\n\n/* Layout Components */\n.container {\n  max-width: var(--container-max-width);\n  margin: 0 auto;\n  padding: 0 var(--container-padding);\n}\n\n.section-title {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n  margin-bottom: var(--space-2xl);\n  text-align: center;\n  color: var(--color-black);\n}\n\n/* Enhanced Utility Classes */\n.fade-in {\n  opacity: 0;\n  transform: translateY(20px);\n  transition: opacity var(--transition-slow), transform var(--transition-slow);\n}\n\n.fade-in.visible {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* Advanced Animation Classes */\n.animate-fade-in-up {\n  animation: fadeInUp var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-down {\n  animation: fadeInDown var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-left {\n  animation: fadeInLeft var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-right {\n  animation: fadeInRight var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-scale-in {\n  animation: scaleIn var(--duration-normal) var(--transition-bounce) forwards;\n}\n\n.animate-slide-in-bottom {\n  animation: slideInFromBottom var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-float {\n  animation: float 3s ease-in-out infinite;\n}\n\n.animate-pulse {\n  animation: pulse 2s ease-in-out infinite;\n}\n\n.animate-glow {\n  animation: glow 2s ease-in-out infinite;\n}\n\n/* Hover Enhancement Classes */\n.hover-lift {\n  transition: transform var(--transition-normal), box-shadow var(--transition-normal);\n}\n\n.hover-lift:hover {\n  transform: translateY(-8px);\n  box-shadow: var(--shadow-xl);\n}\n\n.hover-scale {\n  transition: transform var(--transition-bounce);\n}\n\n.hover-scale:hover {\n  transform: scale(1.05);\n}\n\n.hover-glow {\n  transition: box-shadow var(--transition-normal);\n}\n\n.hover-glow:hover {\n  box-shadow: var(--shadow-glow);\n}\n\n/* Loading States */\n.loading-shimmer {\n  background: linear-gradient(\n    90deg,\n    var(--color-very-light-grey) 0%,\n    var(--color-white) 50%,\n    var(--color-very-light-grey) 100%\n  );\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n/* Intersection Observer Enhanced Classes */\n.reveal {\n  opacity: 0;\n  transform: translateY(50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal.revealed {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.reveal-scale {\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all var(--duration-normal) var(--transition-bounce);\n}\n\n.reveal-scale.revealed {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.reveal-slide-left {\n  opacity: 0;\n  transform: translateX(-50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal-slide-left.revealed {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n.reveal-slide-right {\n  opacity: 0;\n  transform: translateX(50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal-slide-right.revealed {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* Advanced Keyframe Animations */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInDown {\n  0% {\n    opacity: 0;\n    transform: translateY(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInLeft {\n  0% {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes fadeInRight {\n  0% {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes scaleIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes slideInFromBottom {\n  0% {\n    opacity: 0;\n    transform: translateY(50px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n@keyframes glow {\n  0%, 100% {\n    box-shadow: 0 0 5px var(--color-copper);\n  }\n  50% {\n    box-shadow: 0 0 20px var(--color-copper), 0 0 30px var(--color-copper-light);\n  }\n}\n\n/* Enhanced Hero Section Styles */\n.hero-section {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: var(--color-black);\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  will-change: transform;\n}\n\n.hero-image {\n  object-fit: cover;\n  object-position: top;\n  transition: transform var(--duration-slower) ease-out;\n  will-change: transform;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.7) 0%,\n    rgba(21, 41, 96, 0.5) 50%,\n    rgba(0, 0, 0, 0.8) 100%\n  );\n  z-index: 2;\n}\n\n/* Add subtle animated overlay patterns */\n.hero-overlay::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(\n    circle at 20% 80%,\n    rgba(51, 80, 184, 0.1) 0%,\n    transparent 50%\n  ),\n  radial-gradient(\n    circle at 80% 20%,\n    rgba(51, 80, 184, 0.1) 0%,\n    transparent 50%\n  );\n  animation: float 6s ease-in-out infinite;\n}\n\n.hero-content {\n  position: relative;\n  z-index: 3;\n  text-align: center;\n  color: var(--color-white);\n  max-width: 900px;\n  padding: 0 var(--space-lg);\n  animation: heroContentReveal 1.2s var(--transition-smooth) forwards;\n}\n\n@keyframes heroContentReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(40px) scale(0.95);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.hero-name {\n  font-family: var(--font-hero);\n  font-size: var(--text-8xl);\n  font-weight: 100;\n  margin-bottom: var(--space-md);\n  text-shadow:\n    2px 2px 4px rgba(0, 0, 0, 0.5),\n    0 0 20px rgba(51, 80, 184, 0.3);\n  color: var(--color-white);\n  font-style: italic;\n  letter-spacing: -0.02em;\n  opacity: 0;\n  animation: heroNameReveal 1s var(--transition-smooth) 0.3s forwards;\n  position: relative;\n}\n\n@keyframes heroNameReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n    filter: blur(10px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n    filter: blur(0);\n  }\n}\n\n.hero-name::after {\n  content: '';\n  position: absolute;\n  bottom: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 0;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);\n  animation: underlineExpand 1s ease-out 1.3s forwards;\n}\n\n@keyframes underlineExpand {\n  0% {\n    width: 0;\n  }\n  100% {\n    width: 60%;\n  }\n}\n\n.hero-title {\n  font-size: var(--text-2xl);\n  font-weight: 400;\n  font-family: var(--font-sans);\n  margin-bottom: var(--space-lg);\n  color: var(--color-copper-light);\n  opacity: 0;\n  animation: heroTitleReveal 1s var(--transition-smooth) 0.6s forwards;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n@keyframes heroTitleReveal {\n  0% {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.hero-tagline {\n  font-size: var(--text-xl);\n  font-weight: 300;\n  font-style: italic;\n  opacity: 0;\n  margin-bottom: 0;\n  animation: heroTaglineReveal 1s var(--transition-smooth) 0.9s forwards;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n@keyframes heroTaglineReveal {\n  0% {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  100% {\n    opacity: 0.9;\n    transform: translateX(0);\n  }\n}\n\n/* Responsive Typography */\n@media (max-width: 1200px) {\n  :root {\n    --container-padding: var(--space-lg);\n  }\n}\n\n@media (max-width: 768px) {\n  :root {\n    --text-5xl: 2.5rem;   /* 40px */\n    --text-4xl: 2rem;     /* 32px */\n    --text-3xl: 1.5rem;   /* 24px */\n    --text-6xl: 3rem;     /* 48px */\n    --container-padding: var(--space-md);\n    --space-5xl: 4rem;    /* Reduce section padding on mobile */\n    --space-4xl: 3rem;\n    --space-3xl: 2rem;\n  }\n\n  h1 {\n    font-size: var(--text-4xl);\n  }\n\n  h2 {\n    font-size: var(--text-3xl);\n  }\n\n  p {\n    font-size: var(--text-base);\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n  }\n\n  .hero-title {\n    font-size: var(--text-xl);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-lg);\n  }\n\n  .hero-section {\n    min-height: 500px;\n  }\n}\n\n@media (max-width: 480px) {\n  :root {\n    --text-6xl: 2.5rem;   /* 40px */\n    --text-5xl: 2rem;     /* 32px */\n    --text-4xl: 1.75rem;  /* 28px */\n    --text-3xl: 1.25rem;  /* 20px */\n    --container-padding: var(--space-sm);\n    --space-5xl: 3rem;\n    --space-4xl: 2rem;\n    --space-3xl: 1.5rem;\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n    line-height: 1.1;\n  }\n\n  .hero-title {\n    font-size: var(--text-lg);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-base);\n  }\n\n  .section-title {\n    font-size: var(--text-3xl);\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;AAIA;;;;AASA;EACE;;;;;;;EASA;;;;EAIA;;;;;AAMF;EACE;;;;;;EAMA;;;;;AAMF;EACE;;;;;;;;;;AAWF;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;;;;;AASA;;;;;;AAMA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAYA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;;;AAoBA;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;AAaA;;;;;;;;;;;;AAYA;;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;EACE;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;;EAKA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/styles/components.css"], "sourcesContent": ["/* Enhanced Section Styles */\nsection {\n  padding: var(--space-5xl) 0;\n  position: relative;\n  overflow: hidden;\n}\n\nsection:nth-child(even) {\n  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);\n}\n\nsection:nth-child(odd) {\n  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);\n}\n\n/* Add subtle section separators */\nsection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);\n  opacity: 0.3;\n}\n\nsection:first-child::before {\n  display: none;\n}\n\n/* Section entrance animations */\nsection.revealed {\n  animation: sectionReveal 0.8s var(--transition-smooth) forwards;\n}\n\n@keyframes sectionReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Enhanced About Section */\n.about-section {\n  position: relative;\n}\n\n.about-section::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);\n  opacity: 0.2;\n}\n\n.about-section .about-content {\n  max-width: 900px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n}\n\n.about-section .about-content::before {\n  content: '';\n  position: absolute;\n  top: -20px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40px;\n  height: 40px;\n  background: radial-gradient(circle, var(--color-copper) 2px, transparent 2px);\n  opacity: 0.3;\n}\n\n.about-section p {\n  font-size: var(--text-lg);\n  line-height: 1.8;\n  margin-bottom: var(--space-xl);\n  position: relative;\n  padding: 0 var(--space-lg);\n}\n\n.about-section p:first-child {\n  font-size: var(--text-xl);\n  font-weight: 500;\n  color: var(--color-black);\n  margin-bottom: var(--space-2xl);\n}\n\n.about-section p:last-child {\n  margin-bottom: 0;\n}\n\n/* Add subtle text reveal animation */\n.about-section p.revealed {\n  animation: textReveal 0.8s var(--transition-smooth) forwards;\n}\n\n@keyframes textReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Enhanced Academic Journey Section */\n.academic-section {\n  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);\n  position: relative;\n}\n\n.academic-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background:\n    radial-gradient(circle at 20% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.timeline {\n  max-width: 900px;\n  margin: 0 auto;\n  position: relative;\n  padding: var(--space-xl) 0;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: linear-gradient(\n    to bottom,\n    transparent 0%,\n    var(--color-copper) 10%,\n    var(--color-copper) 90%,\n    transparent 100%\n  );\n  transform: translateX(-50%);\n  box-shadow: 0 0 10px rgba(51, 80, 184, 0.3);\n}\n\n.timeline-item {\n  display: flex;\n  margin-bottom: var(--space-4xl);\n  position: relative;\n  opacity: 0;\n  transform: translateX(-30px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.timeline-item.revealed {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: var(--space-md);\n  width: 16px;\n  height: 16px;\n  background: radial-gradient(circle, var(--color-copper) 0%, var(--color-copper-dark) 100%);\n  border: 3px solid var(--color-white);\n  border-radius: 50%;\n  transform: translateX(-50%);\n  z-index: 2;\n  box-shadow: 0 0 0 3px rgba(51, 80, 184, 0.2);\n  transition: all var(--transition-normal);\n}\n\n.timeline-item:hover::before {\n  transform: translateX(-50%) scale(1.2);\n  box-shadow: 0 0 0 6px rgba(51, 80, 184, 0.3);\n}\n\n.timeline-date {\n  flex: 1;\n  text-align: right;\n  padding-right: var(--space-xl);\n  font-weight: 600;\n  color: var(--color-copper);\n  font-size: var(--text-lg);\n  position: relative;\n  transition: all var(--transition-normal);\n}\n\n.timeline-date::after {\n  content: '';\n  position: absolute;\n  right: var(--space-lg);\n  top: 50%;\n  transform: translateY(-50%);\n  width: 0;\n  height: 2px;\n  background: var(--color-copper);\n  transition: width var(--transition-normal);\n}\n\n.timeline-item:hover .timeline-date::after {\n  width: 20px;\n}\n\n.timeline-content {\n  flex: 1;\n  padding-left: var(--space-xl);\n  background: var(--color-white);\n  border-radius: 12px;\n  padding: var(--space-lg);\n  box-shadow: var(--shadow-md);\n  transition: all var(--transition-normal);\n  position: relative;\n}\n\n.timeline-content::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: var(--space-lg);\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-right: 8px solid var(--color-white);\n}\n\n.timeline-item:hover .timeline-content {\n  transform: translateX(10px);\n  box-shadow: var(--shadow-lg);\n}\n\n.timeline-content h3 {\n  font-size: var(--text-xl);\n  margin-bottom: var(--space-sm);\n  color: var(--color-black);\n  position: relative;\n}\n\n.timeline-content h3::after {\n  content: '';\n  position: absolute;\n  bottom: -4px;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background: var(--color-copper);\n  transition: width var(--transition-normal);\n}\n\n.timeline-item:hover .timeline-content h3::after {\n  width: 40px;\n}\n\n.timeline-content p {\n  font-size: var(--text-base);\n  color: var(--color-medium-grey);\n  margin-bottom: 0;\n  line-height: 1.6;\n}\n\n/* Academic Achievement Section */\n.academic-achievement {\n  max-width: 800px;\n  margin: var(--space-3xl) auto 0;\n  padding: var(--space-xl);\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border-left: 4px solid var(--color-copper);\n}\n\n.academic-achievement h3 {\n  font-size: var(--text-xl);\n  margin-bottom: var(--space-md);\n  color: var(--color-black);\n}\n\n.academic-achievement p {\n  font-size: var(--text-lg);\n  color: var(--color-medium-grey);\n  margin-bottom: 0;\n  font-weight: 500;\n}\n\n/* Enhanced Research Interests Section */\n.research-section {\n  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);\n  position: relative;\n}\n\n.research-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background:\n    radial-gradient(circle at 30% 40%, rgba(51, 80, 184, 0.05) 0%, transparent 50%),\n    radial-gradient(circle at 70% 60%, rgba(51, 80, 184, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.interests-list {\n  max-width: 800px;\n  margin: 0 auto;\n  list-style: none;\n  display: grid;\n  gap: var(--space-lg);\n}\n\n.interest-item {\n  padding: var(--space-xl);\n  background: var(--color-white);\n  border-radius: 12px;\n  border-left: 4px solid var(--color-copper);\n  box-shadow: var(--shadow-md);\n  transition: all var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.interest-item.revealed {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.interest-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(51, 80, 184, 0.02) 0%,\n    transparent 50%\n  );\n  opacity: 0;\n  transition: opacity var(--transition-normal);\n}\n\n.interest-item:hover::before {\n  opacity: 1;\n}\n\n.interest-item:hover {\n  transform: translateX(12px) translateY(-4px);\n  box-shadow: var(--shadow-xl);\n  border-left-color: var(--color-copper-dark);\n}\n\n.interest-item:last-child {\n  margin-bottom: 0;\n}\n\n/* Add icon placeholder for future enhancement */\n.interest-item::after {\n  content: '→';\n  position: absolute;\n  right: var(--space-lg);\n  top: var(--space-lg);\n  color: var(--color-copper);\n  font-size: var(--text-xl);\n  opacity: 0;\n  transform: translateX(-10px);\n  transition: all var(--transition-normal);\n}\n\n.interest-item:hover::after {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* Enhanced Achievements Section */\n.achievements-section {\n  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);\n  position: relative;\n}\n\n.achievements-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background:\n    radial-gradient(circle at 25% 25%, rgba(51, 80, 184, 0.03) 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.achievements-list {\n  max-width: 900px;\n  margin: 0 auto;\n  list-style: none;\n  display: grid;\n  gap: var(--space-xl);\n}\n\n.achievement-item {\n  padding: var(--space-xl);\n  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);\n  border-radius: 16px;\n  position: relative;\n  transition: all var(--transition-normal);\n  box-shadow: var(--shadow-md);\n  border: 1px solid rgba(51, 80, 184, 0.1);\n  opacity: 0;\n  transform: translateY(30px) scale(0.95);\n  overflow: hidden;\n}\n\n.achievement-item.revealed {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n.achievement-item::before {\n  content: '★';\n  position: absolute;\n  left: var(--space-lg);\n  top: var(--space-lg);\n  color: var(--color-copper);\n  font-size: var(--text-2xl);\n  transition: all var(--transition-normal);\n  z-index: 2;\n}\n\n.achievement-item::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(51, 80, 184, 0.05) 0%,\n    transparent 50%\n  );\n  opacity: 0;\n  transition: opacity var(--transition-normal);\n}\n\n.achievement-item:hover::after {\n  opacity: 1;\n}\n\n.achievement-item {\n  padding-left: var(--space-5xl);\n}\n\n.achievement-item:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--color-copper);\n}\n\n.achievement-item:hover::before {\n  transform: scale(1.2) rotate(72deg);\n  color: var(--color-copper-dark);\n  text-shadow: 0 0 10px rgba(51, 80, 184, 0.5);\n}\n\n.achievement-item:last-child {\n  margin-bottom: 0;\n}\n\n.achievement-item h3 {\n  font-size: var(--text-xl);\n  margin-bottom: var(--space-sm);\n  color: var(--color-black);\n  position: relative;\n  z-index: 2;\n}\n\n.achievement-item p {\n  color: var(--color-medium-grey);\n  line-height: 1.6;\n  position: relative;\n  z-index: 2;\n}\n\n/* Enhanced Affiliations Section */\n.affiliations-section {\n  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);\n  position: relative;\n}\n\n.affiliations-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background:\n    radial-gradient(circle at 40% 30%, rgba(51, 80, 184, 0.04) 0%, transparent 50%),\n    radial-gradient(circle at 60% 70%, rgba(51, 80, 184, 0.04) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.affiliations-subtitle {\n  text-align: center;\n  font-size: var(--text-lg);\n  color: var(--color-medium-grey);\n  margin-bottom: var(--space-3xl);\n  position: relative;\n}\n\n.affiliations-subtitle::after {\n  content: '';\n  position: absolute;\n  bottom: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);\n}\n\n.logos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: var(--space-2xl);\n  max-width: 700px;\n  margin: 0 auto;\n  align-items: center;\n}\n\n.logo-item {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-xl);\n  background: var(--color-white);\n  border-radius: 16px;\n  box-shadow: var(--shadow-md);\n  transition: all var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n  opacity: 0;\n  transform: translateY(30px) scale(0.9);\n  border: 1px solid rgba(51, 80, 184, 0.1);\n}\n\n.logo-item.revealed {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n.logo-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(51, 80, 184, 0.05) 0%,\n    transparent 50%\n  );\n  opacity: 0;\n  transition: opacity var(--transition-normal);\n}\n\n.logo-item:hover::before {\n  opacity: 1;\n}\n\n.logo-item:hover {\n  transform: translateY(-8px) scale(1.05);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--color-copper);\n}\n\n.institution-logo {\n  filter: grayscale(100%);\n  opacity: 0.7;\n  transition: all var(--transition-normal);\n  max-width: 100%;\n  height: auto;\n  position: relative;\n  z-index: 2;\n}\n\n.logo-item:hover .institution-logo {\n  filter: grayscale(0%);\n  opacity: 1;\n  transform: scale(1.1);\n}\n\n/* Future Aspirations Section */\n.aspirations-section {\n  background-color: var(--color-dark-grey);\n  color: var(--color-white);\n}\n\n.aspirations-section .section-title {\n  color: var(--color-white);\n}\n\n.aspirations-quote {\n  max-width: 900px;\n  margin: 0 auto;\n  text-align: center;\n  font-style: italic;\n  position: relative;\n}\n\n.aspirations-quote::before {\n  content: '\"';\n  font-size: 4rem;\n  color: var(--color-copper);\n  position: absolute;\n  top: -1rem;\n  left: -2rem;\n  font-family: var(--font-serif);\n}\n\n.aspirations-quote p {\n  font-size: var(--text-xl);\n  line-height: 1.8;\n  margin-bottom: var(--space-xl);\n  color: var(--color-very-light-grey);\n}\n\n.aspirations-quote p:last-child {\n  margin-bottom: 0;\n}\n\n/* Enhanced Contact Section */\n.contact-section {\n  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);\n  position: relative;\n}\n\n.contact-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background:\n    radial-gradient(circle at 50% 20%, rgba(51, 80, 184, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.contact-description {\n  text-align: center;\n  max-width: 700px;\n  margin: 0 auto var(--space-3xl);\n  color: var(--color-medium-grey);\n  font-size: var(--text-lg);\n  line-height: 1.7;\n  position: relative;\n}\n\n.contact-description::after {\n  content: '';\n  position: absolute;\n  bottom: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 80px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);\n}\n\n.contact-info {\n  text-align: center;\n  position: relative;\n}\n\n.email-link {\n  display: inline-block;\n  font-size: var(--text-xl);\n  font-weight: 500;\n  padding: var(--space-lg) var(--space-2xl);\n  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);\n  color: var(--color-white);\n  border-radius: 50px;\n  margin-bottom: var(--space-xl);\n  transition: all var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n  text-decoration: none;\n  box-shadow: var(--shadow-md);\n}\n\n.email-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(255, 255, 255, 0.2),\n    transparent\n  );\n  transition: left var(--duration-slow);\n}\n\n.email-link:hover::before {\n  left: 100%;\n}\n\n.email-link:hover {\n  color: var(--color-white);\n  transform: translateY(-4px) scale(1.05);\n  box-shadow: var(--shadow-xl);\n  background: linear-gradient(135deg, var(--color-copper-dark) 0%, var(--color-copper) 100%);\n}\n\n.social-links {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: var(--space-lg);\n  flex-wrap: wrap;\n}\n\n.social-placeholder {\n  color: var(--color-light-grey);\n  font-size: var(--text-sm);\n}\n\n.social-note {\n  color: var(--color-light-grey);\n  font-size: var(--text-xs);\n  font-style: italic;\n}\n\n/* Animation Enhancements */\n.hero-content {\n  animation: heroFadeIn 1.5s ease-out;\n}\n\n@keyframes heroFadeIn {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.hero-name {\n  animation: slideInFromLeft 1s ease-out 0.3s both;\n}\n\n.hero-title {\n  animation: slideInFromLeft 1s ease-out 0.6s both;\n}\n\n.hero-tagline {\n  animation: slideInFromLeft 1s ease-out 0.9s both;\n}\n\n@keyframes slideInFromLeft {\n  0% {\n    opacity: 0;\n    transform: translateX(-50px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Enhanced Page Loading Animation */\n@keyframes pageLoad {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\nbody {\n  animation: pageLoad 0.6s ease-out;\n}\n\n/* Enhanced Section Title Animations */\n.section-title {\n  position: relative;\n  overflow: hidden;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 0;\n  height: 3px;\n  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));\n  transition: width var(--duration-slow) var(--transition-smooth);\n}\n\n.section-title.revealed::before {\n  width: 60px;\n}\n\n/* Smooth Focus States for Accessibility */\n*:focus {\n  outline: 2px solid var(--color-copper);\n  outline-offset: 2px;\n  border-radius: 4px;\n}\n\n/* Enhanced Print Styles */\n@media print {\n  .hero-section {\n    height: auto;\n    min-height: auto;\n    page-break-inside: avoid;\n  }\n\n  .hero-overlay,\n  .hero-background::before {\n    display: none;\n  }\n\n  section {\n    page-break-inside: avoid;\n    break-inside: avoid;\n  }\n}\n\n/* Staggered animation for lists */\n.interests-list .interest-item:nth-child(1) { animation-delay: 0.1s; }\n.interests-list .interest-item:nth-child(2) { animation-delay: 0.2s; }\n.interests-list .interest-item:nth-child(3) { animation-delay: 0.3s; }\n.interests-list .interest-item:nth-child(4) { animation-delay: 0.4s; }\n.interests-list .interest-item:nth-child(5) { animation-delay: 0.5s; }\n\n.achievements-list .achievement-item:nth-child(1) { animation-delay: 0.1s; }\n.achievements-list .achievement-item:nth-child(2) { animation-delay: 0.25s; }\n.achievements-list .achievement-item:nth-child(3) { animation-delay: 0.4s; }\n.achievements-list .achievement-item:nth-child(4) { animation-delay: 0.55s; }\n\n/* Tablet Responsive Styles */\n@media (max-width: 1024px) {\n  .timeline-item {\n    margin-bottom: var(--space-2xl);\n  }\n\n  .logos-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--space-xl);\n  }\n\n  .aspirations-quote {\n    max-width: 700px;\n  }\n}\n\n/* Mobile Responsive Styles */\n@media (max-width: 768px) {\n  section {\n    padding: var(--space-3xl) 0;\n  }\n\n  /* Timeline adjustments for mobile */\n  .timeline::before {\n    left: var(--space-lg);\n  }\n\n  .timeline-item {\n    flex-direction: column;\n    padding-left: var(--space-2xl);\n  }\n\n  .timeline-item::before {\n    left: var(--space-lg);\n    transform: translateX(-50%);\n  }\n\n  .timeline-date {\n    text-align: left;\n    padding-right: 0;\n    padding-bottom: var(--space-sm);\n    font-size: var(--text-base);\n  }\n\n  .timeline-content {\n    padding-left: 0;\n  }\n\n  /* Logos grid for mobile */\n  .logos-grid {\n    grid-template-columns: 1fr;\n    gap: var(--space-lg);\n  }\n\n  /* Aspirations quote for mobile */\n  .aspirations-quote::before {\n    font-size: 3rem;\n    top: -0.5rem;\n    left: -1rem;\n  }\n\n  .aspirations-quote p {\n    font-size: var(--text-lg);\n  }\n\n  /* Contact section for mobile */\n  .email-link {\n    font-size: var(--text-lg);\n    padding: var(--space-md) var(--space-lg);\n  }\n\n  .social-links {\n    flex-direction: column;\n    gap: var(--space-sm);\n  }\n\n  /* Disable hover effects on mobile */\n  .timeline-item:hover {\n    transform: none;\n  }\n\n  .achievement-item:hover {\n    transform: none;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n}\n\n/* Small Mobile Responsive Styles */\n@media (max-width: 480px) {\n  section {\n    padding: var(--space-2xl) 0;\n  }\n\n  .about-section .about-content {\n    max-width: 100%;\n  }\n\n  .timeline {\n    max-width: 100%;\n  }\n\n  .timeline-item {\n    padding-left: var(--space-xl);\n  }\n\n  .timeline-item::before {\n    left: var(--space-sm);\n  }\n\n  .timeline::before {\n    left: var(--space-sm);\n  }\n\n  .interests-list {\n    max-width: 100%;\n  }\n\n  .achievements-list {\n    max-width: 100%;\n  }\n\n  .achievement-item {\n    padding: var(--space-lg);\n    padding-left: var(--space-3xl);\n  }\n\n  .aspirations-quote {\n    max-width: 100%;\n  }\n\n  .aspirations-quote::before {\n    font-size: 2.5rem;\n    top: -0.25rem;\n    left: -0.5rem;\n  }\n\n  .email-link {\n    font-size: var(--text-base);\n    padding: var(--space-sm) var(--space-md);\n    display: block;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;AAkBA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAYA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAWA;;;;AAKA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;AAOA;EACE;;;;;;EAMA;;;;EAKA;;;;;;AAOF;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;EACE;;;;EAIA;;;;;EAKA;;;;;AAMF;EACE;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;;EAMA;;;;EAKA;;;;;EAKA;;;;;EAMA;;;;EAIA;;;;;;AAOF;EACE;;;;EAIA;;;;EAQA;;;;EAIA;;;;EAQA;;;;EAQA;;;;;EAKA;;;;EAIA;;;;;;EAMA", "debugId": null}}]}