{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/globals.css"], "sourcesContent": ["/* CSS Custom Properties */\n:root {\n  /* Colors - Greyscale with copper accent */\n  --color-black: #0a0a0a;\n  --color-dark-grey: #0a1322;\n  --color-medium-grey: #4a4a4a;\n  --color-light-grey: #8a8a8a;\n  --color-very-light-grey: #e5e5e5;\n  --color-white: #ffffff;\n  --color-copper: #3350b8;\n  --color-copper-light: #749ed4;\n  --color-copper-dark: #2b458b;\n\n  /* Typography */\n  --font-serif: 'Playfair Display', Georgia, serif;\n  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  --font-hero: 'Old Standard TT', Georgia, serif;\n\n  /* Font Sizes */\n  --text-xs: 0.75rem;    /* 12px */\n  --text-sm: 0.875rem;   /* 14px */\n  --text-base: 1rem;     /* 16px */\n  --text-lg: 1.125rem;   /* 18px */\n  --text-xl: 1.25rem;    /* 20px */\n  --text-2xl: 1.5rem;    /* 24px */\n  --text-3xl: 1.875rem;  /* 30px */\n  --text-4xl: 2.25rem;   /* 36px */\n  --text-5xl: 3rem;      /* 48px */\n  --text-6xl: 3.75rem;   /* 60px */\n  --text-8xl: 8.75rem;   /* 60px */\n\n  /* Spacing */\n  --space-xs: 0.5rem;    /* 8px */\n  --space-sm: 0.75rem;   /* 12px */\n  --space-md: 1rem;      /* 16px */\n  --space-lg: 1.5rem;    /* 24px */\n  --space-xl: 2rem;      /* 32px */\n  --space-2xl: 3rem;     /* 48px */\n  --space-3xl: 4rem;     /* 64px */\n  --space-4xl: 6rem;     /* 96px */\n  --space-5xl: 8rem;     /* 128px */\n\n  /* Layout */\n  --container-max-width: 1200px;\n  --container-padding: var(--space-lg);\n\n  /* Transitions */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s ease;\n  --transition-slow: 0.5s ease;\n}\n\n/* Reset and Base Styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n}\n\nbody {\n  font-family: var(--font-sans);\n  font-weight: 400;\n  line-height: 1.6;\n  color: var(--color-dark-grey);\n  background-color: var(--color-white);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Typography Styles */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-serif);\n  font-weight: 600;\n  line-height: 1.2;\n  margin-bottom: var(--space-md);\n  color: var(--color-black);\n}\n\nh1 {\n  font-size: var(--text-5xl);\n  font-weight: 700;\n}\n\nh2 {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n}\n\nh3 {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n}\n\nh4 {\n  font-size: var(--text-xl);\n  font-weight: 500;\n}\n\np {\n  margin-bottom: var(--space-lg);\n  font-size: var(--text-lg);\n  line-height: 1.7;\n}\n\na {\n  color: var(--color-copper);\n  text-decoration: none;\n  transition: color var(--transition-fast);\n}\n\na:hover {\n  color: var(--color-copper-dark);\n}\n\n/* Layout Components */\n.container {\n  max-width: var(--container-max-width);\n  margin: 0 auto;\n  padding: 0 var(--container-padding);\n}\n\n.section-title {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n  margin-bottom: var(--space-2xl);\n  text-align: center;\n  color: var(--color-black);\n}\n\n/* Utility Classes */\n.fade-in {\n  opacity: 0;\n  transform: translateY(20px);\n  transition: opacity var(--transition-slow), transform var(--transition-slow);\n}\n\n.fade-in.visible {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* Loading Animation */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Hero Section Styles */\n.hero-section {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.hero-image {\n  object-fit: cover;\n  object-position: top;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #000000b2 0%, #15296080 50%, #000c 100%);\n  z-index: 2;\n}\n\n.hero-content {\n  position: relative;\n  z-index: 3;\n  text-align: center;\n  color: var(--color-white);\n  max-width: 800px;\n  padding: 0 var(--space-lg);\n}\n\n.hero-name {\n  font-family: var(--font-hero);\n  font-size: var(--text-8xl);\n  font-weight: 100;\n  margin-bottom: var(--space-md);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  color: var(--color-white);\n  font-style: italic;\n  letter-spacing: -9%;\n}\n\n.hero-title {\n  font-size: var(--text-2xl);\n  font-weight: 400;\n  font-family: var(--font-sans);\n  margin-bottom: var(--space-lg);\n  color: var(--color-copper-light);\n}\n\n.hero-tagline {\n  font-size: var(--text-xl);\n  font-weight: 300;\n  font-style: italic;\n  opacity: 0.9;\n  margin-bottom: 0;\n}\n\n/* Responsive Typography */\n@media (max-width: 1200px) {\n  :root {\n    --container-padding: var(--space-lg);\n  }\n}\n\n@media (max-width: 768px) {\n  :root {\n    --text-5xl: 2.5rem;   /* 40px */\n    --text-4xl: 2rem;     /* 32px */\n    --text-3xl: 1.5rem;   /* 24px */\n    --text-6xl: 3rem;     /* 48px */\n    --container-padding: var(--space-md);\n    --space-5xl: 4rem;    /* Reduce section padding on mobile */\n    --space-4xl: 3rem;\n    --space-3xl: 2rem;\n  }\n\n  h1 {\n    font-size: var(--text-4xl);\n  }\n\n  h2 {\n    font-size: var(--text-3xl);\n  }\n\n  p {\n    font-size: var(--text-base);\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n  }\n\n  .hero-title {\n    font-size: var(--text-xl);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-lg);\n  }\n\n  .hero-section {\n    min-height: 500px;\n  }\n}\n\n@media (max-width: 480px) {\n  :root {\n    --text-6xl: 2.5rem;   /* 40px */\n    --text-5xl: 2rem;     /* 32px */\n    --text-4xl: 1.75rem;  /* 28px */\n    --text-3xl: 1.25rem;  /* 20px */\n    --container-padding: var(--space-sm);\n    --space-5xl: 3rem;\n    --space-4xl: 2rem;\n    --space-3xl: 1.5rem;\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n    line-height: 1.1;\n  }\n\n  .hero-title {\n    font-size: var(--text-lg);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-base);\n  }\n\n  .section-title {\n    font-size: var(--text-3xl);\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;;;;;AASA;;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;;AASA;EACE;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;;EAKA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/styles/components.css"], "sourcesContent": ["/* Section Styles */\nsection {\n  padding: var(--space-5xl) 0;\n}\n\nsection:nth-child(even) {\n  background-color: #fafafa;\n}\n\n/* About Section */\n.about-section .about-content {\n  max-width: 800px;\n  margin: 0 auto;\n  text-align: center;\n}\n\n.about-section p {\n  font-size: var(--text-lg);\n  line-height: 1.8;\n  margin-bottom: var(--space-xl);\n}\n\n.about-section p:last-child {\n  margin-bottom: 0;\n}\n\n/* Academic Journey Section */\n.academic-section {\n  background-color: var(--color-white);\n}\n\n.timeline {\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: var(--color-copper);\n  transform: translateX(-50%);\n}\n\n.timeline-item {\n  display: flex;\n  margin-bottom: var(--space-3xl);\n  position: relative;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: var(--space-md);\n  width: 12px;\n  height: 12px;\n  background: var(--color-copper);\n  border-radius: 50%;\n  transform: translateX(-50%);\n  z-index: 2;\n}\n\n.timeline-date {\n  flex: 1;\n  text-align: right;\n  padding-right: var(--space-xl);\n  font-weight: 600;\n  color: var(--color-copper);\n  font-size: var(--text-lg);\n}\n\n.timeline-content {\n  flex: 1;\n  padding-left: var(--space-xl);\n}\n\n.timeline-content h3 {\n  font-size: var(--text-xl);\n  margin-bottom: var(--space-sm);\n  color: var(--color-black);\n}\n\n.timeline-content p {\n  font-size: var(--text-base);\n  color: var(--color-medium-grey);\n  margin-bottom: 0;\n}\n\n/* Academic Achievement Section */\n.academic-achievement {\n  max-width: 800px;\n  margin: var(--space-3xl) auto 0;\n  padding: var(--space-xl);\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border-left: 4px solid var(--color-copper);\n}\n\n.academic-achievement h3 {\n  font-size: var(--text-xl);\n  margin-bottom: var(--space-md);\n  color: var(--color-black);\n}\n\n.academic-achievement p {\n  font-size: var(--text-lg);\n  color: var(--color-medium-grey);\n  margin-bottom: 0;\n  font-weight: 500;\n}\n\n/* Research Interests Section */\n.research-section {\n  background-color: #fafafa;\n}\n\n.interests-list {\n  max-width: 700px;\n  margin: 0 auto;\n  list-style: none;\n}\n\n.interest-item {\n  padding: var(--space-lg);\n  margin-bottom: var(--space-md);\n  background: var(--color-white);\n  border-left: 4px solid var(--color-copper);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  /* transition: transform var(--transition-fast), box-shadow var(--transition-fast); */\n}\n\n.interest-item:hover {\n  transform: translateX(8px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n.interest-item:last-child {\n  margin-bottom: 0;\n}\n\n/* Achievements Section */\n.achievements-section {\n  background-color: var(--color-white);\n}\n\n.achievements-list {\n  max-width: 800px;\n  margin: 0 auto;\n  list-style: none;\n}\n\n.achievement-item {\n  padding: var(--space-xl);\n  margin-bottom: var(--space-lg);\n  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);\n  border-radius: 8px;\n  position: relative;\n  /* transition: transform var(--transition-fast); */\n}\n\n.achievement-item::before {\n  content: '★';\n  position: absolute;\n  left: var(--space-lg);\n  top: var(--space-lg);\n  color: var(--color-copper);\n  font-size: var(--text-xl);\n}\n\n.achievement-item {\n  padding-left: var(--space-4xl);\n}\n\n.achievement-item:hover {\n  transform: translateY(-2px);\n}\n\n.achievement-item:last-child {\n  margin-bottom: 0;\n}\n\n/* Affiliations Section */\n.affiliations-section {\n  background-color: #fafafa;\n}\n\n.affiliations-subtitle {\n  text-align: center;\n  font-size: var(--text-lg);\n  color: var(--color-medium-grey);\n  margin-bottom: var(--space-2xl);\n}\n\n.logos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: var(--space-2xl);\n  max-width: 600px;\n  margin: 0 auto;\n  align-items: center;\n}\n\n.logo-item {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-lg);\n  background: var(--color-white);\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  /* transition: transform var(--transition-fast); */\n}\n\n.logo-item:hover {\n  transform: scale(1.05);\n}\n\n.institution-logo {\n  filter: grayscale(100%);\n  opacity: 0.8;\n  /* transition: filter var(--transition-fast), opacity var(--transition-fast); */\n}\n\n.logo-item:hover .institution-logo {\n  filter: grayscale(0%);\n  opacity: 1;\n}\n\n/* Future Aspirations Section */\n.aspirations-section {\n  background-color: var(--color-dark-grey);\n  color: var(--color-white);\n}\n\n.aspirations-section .section-title {\n  color: var(--color-white);\n}\n\n.aspirations-quote {\n  max-width: 900px;\n  margin: 0 auto;\n  text-align: center;\n  font-style: italic;\n  position: relative;\n}\n\n.aspirations-quote::before {\n  content: '\"';\n  font-size: 4rem;\n  color: var(--color-copper);\n  position: absolute;\n  top: -1rem;\n  left: -2rem;\n  font-family: var(--font-serif);\n}\n\n.aspirations-quote p {\n  font-size: var(--text-xl);\n  line-height: 1.8;\n  margin-bottom: var(--space-xl);\n  color: var(--color-very-light-grey);\n}\n\n.aspirations-quote p:last-child {\n  margin-bottom: 0;\n}\n\n/* Contact Section */\n.contact-section {\n  background-color: var(--color-white);\n}\n\n.contact-description {\n  text-align: center;\n  max-width: 600px;\n  margin: 0 auto var(--space-2xl);\n  color: var(--color-medium-grey);\n}\n\n.contact-info {\n  text-align: center;\n}\n\n.email-link {\n  display: inline-block;\n  font-size: var(--text-xl);\n  font-weight: 500;\n  padding: var(--space-lg) var(--space-xl);\n  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);\n  color: var(--color-white);\n  border-radius: 8px;\n  margin-bottom: var(--space-xl);\n  /* transition: transform var(--transition-fast), box-shadow var(--transition-fast); */\n}\n\n.email-link:hover {\n  color: var(--color-white);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(184, 115, 51, 0.3);\n}\n\n.social-links {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: var(--space-lg);\n  flex-wrap: wrap;\n}\n\n.social-placeholder {\n  color: var(--color-light-grey);\n  font-size: var(--text-sm);\n}\n\n.social-note {\n  color: var(--color-light-grey);\n  font-size: var(--text-xs);\n  font-style: italic;\n}\n\n/* Animation Enhancements */\n.hero-content {\n  animation: heroFadeIn 1.5s ease-out;\n}\n\n@keyframes heroFadeIn {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.hero-name {\n  animation: slideInFromLeft 1s ease-out 0.3s both;\n}\n\n.hero-title {\n  animation: slideInFromLeft 1s ease-out 0.6s both;\n}\n\n.hero-tagline {\n  animation: slideInFromLeft 1s ease-out 0.9s both;\n}\n\n@keyframes slideInFromLeft {\n  0% {\n    opacity: 0;\n    transform: translateX(-50px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Enhanced hover effects */\n.timeline-item {\n  /* transition: all var(--transition-normal); */\n}\n\n.timeline-item:hover {\n  transform: translateX(10px);\n}\n\n.timeline-item:hover .timeline-date {\n  color: var(--color-copper-dark);\n}\n\n.achievement-item {\n  /* transition: all var(--transition-normal); */\n}\n\n.achievement-item:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.achievement-item:hover::before {\n  transform: scale(1.2);\n  /* transition: transform var(--transition-fast); */\n}\n\n/* Staggered animation for lists */\n.interests-list .interest-item:nth-child(1) { animation-delay: 0.1s; }\n.interests-list .interest-item:nth-child(2) { animation-delay: 0.2s; }\n.interests-list .interest-item:nth-child(3) { animation-delay: 0.3s; }\n.interests-list .interest-item:nth-child(4) { animation-delay: 0.4s; }\n.interests-list .interest-item:nth-child(5) { animation-delay: 0.5s; }\n\n.achievements-list .achievement-item:nth-child(1) { animation-delay: 0.1s; }\n.achievements-list .achievement-item:nth-child(2) { animation-delay: 0.25s; }\n.achievements-list .achievement-item:nth-child(3) { animation-delay: 0.4s; }\n.achievements-list .achievement-item:nth-child(4) { animation-delay: 0.55s; }\n\n/* Tablet Responsive Styles */\n@media (max-width: 1024px) {\n  .timeline-item {\n    margin-bottom: var(--space-2xl);\n  }\n\n  .logos-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--space-xl);\n  }\n\n  .aspirations-quote {\n    max-width: 700px;\n  }\n}\n\n/* Mobile Responsive Styles */\n@media (max-width: 768px) {\n  section {\n    padding: var(--space-3xl) 0;\n  }\n\n  /* Timeline adjustments for mobile */\n  .timeline::before {\n    left: var(--space-lg);\n  }\n\n  .timeline-item {\n    flex-direction: column;\n    padding-left: var(--space-2xl);\n  }\n\n  .timeline-item::before {\n    left: var(--space-lg);\n    transform: translateX(-50%);\n  }\n\n  .timeline-date {\n    text-align: left;\n    padding-right: 0;\n    padding-bottom: var(--space-sm);\n    font-size: var(--text-base);\n  }\n\n  .timeline-content {\n    padding-left: 0;\n  }\n\n  /* Logos grid for mobile */\n  .logos-grid {\n    grid-template-columns: 1fr;\n    gap: var(--space-lg);\n  }\n\n  /* Aspirations quote for mobile */\n  .aspirations-quote::before {\n    font-size: 3rem;\n    top: -0.5rem;\n    left: -1rem;\n  }\n\n  .aspirations-quote p {\n    font-size: var(--text-lg);\n  }\n\n  /* Contact section for mobile */\n  .email-link {\n    font-size: var(--text-lg);\n    padding: var(--space-md) var(--space-lg);\n  }\n\n  .social-links {\n    flex-direction: column;\n    gap: var(--space-sm);\n  }\n\n  /* Disable hover effects on mobile */\n  .timeline-item:hover {\n    transform: none;\n  }\n\n  .achievement-item:hover {\n    transform: none;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n}\n\n/* Small Mobile Responsive Styles */\n@media (max-width: 480px) {\n  section {\n    padding: var(--space-2xl) 0;\n  }\n\n  .about-section .about-content {\n    max-width: 100%;\n  }\n\n  .timeline {\n    max-width: 100%;\n  }\n\n  .timeline-item {\n    padding-left: var(--space-xl);\n  }\n\n  .timeline-item::before {\n    left: var(--space-sm);\n  }\n\n  .timeline::before {\n    left: var(--space-sm);\n  }\n\n  .interests-list {\n    max-width: 100%;\n  }\n\n  .achievements-list {\n    max-width: 100%;\n  }\n\n  .achievement-item {\n    padding: var(--space-lg);\n    padding-left: var(--space-3xl);\n  }\n\n  .aspirations-quote {\n    max-width: 100%;\n  }\n\n  .aspirations-quote::before {\n    font-size: 2.5rem;\n    top: -0.25rem;\n    left: -0.5rem;\n  }\n\n  .email-link {\n    font-size: var(--text-base);\n    padding: var(--space-sm) var(--space-md);\n    display: block;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;AAIA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;AASA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;AASA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAWA;;;;AAIA;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAQA;;;;;AAKA;;;;AAMA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;EACE;;;;EAIA;;;;;EAKA;;;;;AAMF;EACE;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;;EAMA;;;;EAKA;;;;;EAKA;;;;;EAMA;;;;EAIA;;;;;;AAOF;EACE;;;;EAIA;;;;EAQA;;;;EAIA;;;;EAQA;;;;EAQA;;;;;EAKA;;;;EAIA;;;;;;EAMA", "debugId": null}}]}