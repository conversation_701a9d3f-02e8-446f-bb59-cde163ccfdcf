/* [project]/src/styles/components.css [app-client] (css) */
section {
  padding: var(--space-5xl) 0;
}

section:nth-child(2n) {
  background-color: #fafafa;
}

.about-section .about-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.about-section p {
  font-size: var(--text-lg);
  margin-bottom: var(--space-xl);
  line-height: 1.8;
}

.about-section p:last-child {
  margin-bottom: 0;
}

.academic-section {
  background-color: var(--color-white);
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline:before {
  content: "";
  background: var(--color-copper);
  width: 2px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.timeline-item {
  margin-bottom: var(--space-3xl);
  display: flex;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: "";
  left: 50%;
  top: var(--space-md);
  background: var(--color-copper);
  z-index: 2;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  position: absolute;
  transform: translateX(-50%);
}

.timeline-date {
  text-align: right;
  padding-right: var(--space-xl);
  color: var(--color-copper);
  font-weight: 600;
  font-size: var(--text-lg);
  flex: 1;
}

.timeline-content {
  padding-left: var(--space-xl);
  flex: 1;
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-medium-grey);
  margin-bottom: 0;
}

.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  border-left: 4px solid var(--color-copper);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

.research-section {
  background-color: #fafafa;
}

.interests-list {
  max-width: 700px;
  margin: 0 auto;
  list-style: none;
}

.interest-item {
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  background: var(--color-white);
  border-left: 4px solid var(--color-copper);
  box-shadow: 0 2px 8px #0000001a;
}

.interest-item:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 16px #00000026;
}

.interest-item:last-child {
  margin-bottom: 0;
}

.achievements-section {
  background-color: var(--color-white);
}

.achievements-list {
  max-width: 800px;
  margin: 0 auto;
  list-style: none;
}

.achievement-item {
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  position: relative;
}

.achievement-item:before {
  content: "★";
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-xl);
  position: absolute;
}

.achievement-item {
  padding-left: var(--space-4xl);
}

.achievement-item:hover {
  transform: translateY(-2px);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.affiliations-section {
  background-color: #fafafa;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-2xl);
}

.logos-grid {
  gap: var(--space-2xl);
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
  display: grid;
}

.logo-item {
  padding: var(--space-lg);
  background: var(--color-white);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  display: flex;
  box-shadow: 0 2px 8px #0000001a;
}

.logo-item:hover {
  transform: scale(1.05);
}

.institution-logo {
  filter: grayscale();
  opacity: .8;
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
}

.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  font-style: italic;
  position: relative;
}

.aspirations-quote:before {
  content: "\"";
  color: var(--color-copper);
  font-size: 4rem;
  font-family: var(--font-serif);
  position: absolute;
  top: -1rem;
  left: -2rem;
}

.aspirations-quote p {
  font-size: var(--text-xl);
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
  line-height: 1.8;
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

.contact-section {
  background-color: var(--color-white);
}

.contact-description {
  text-align: center;
  max-width: 600px;
  margin: 0 auto var(--space-2xl);
  color: var(--color-medium-grey);
}

.contact-info {
  text-align: center;
}

.email-link {
  font-size: var(--text-xl);
  padding: var(--space-lg) var(--space-xl);
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  margin-bottom: var(--space-xl);
  border-radius: 8px;
  font-weight: 500;
  display: inline-block;
}

.email-link:hover {
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px #b873334d;
}

.social-links {
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
  display: flex;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

.hero-content {
  animation: 1.5s ease-out heroFadeIn;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: 1s ease-out .3s both slideInFromLeft;
}

.hero-title {
  animation: 1s ease-out .6s both slideInFromLeft;
}

.hero-tagline {
  animation: 1s ease-out .9s both slideInFromLeft;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-item:hover {
  transform: translateX(10px);
}

.timeline-item:hover .timeline-date {
  color: var(--color-copper-dark);
}

.achievement-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #00000026;
}

.achievement-item:hover:before {
  transform: scale(1.2);
}

.interests-list .interest-item:first-child {
  animation-delay: .1s;
}

.interests-list .interest-item:nth-child(2) {
  animation-delay: .2s;
}

.interests-list .interest-item:nth-child(3) {
  animation-delay: .3s;
}

.interests-list .interest-item:nth-child(4) {
  animation-delay: .4s;
}

.interests-list .interest-item:nth-child(5) {
  animation-delay: .5s;
}

.achievements-list .achievement-item:first-child {
  animation-delay: .1s;
}

.achievements-list .achievement-item:nth-child(2) {
  animation-delay: .25s;
}

.achievements-list .achievement-item:nth-child(3) {
  animation-delay: .4s;
}

.achievements-list .achievement-item:nth-child(4) {
  animation-delay: .55s;
}

@media (width <= 1024px) {
  .timeline-item {
    margin-bottom: var(--space-2xl);
  }

  .logos-grid {
    gap: var(--space-xl);
    grid-template-columns: repeat(2, 1fr);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

@media (width <= 768px) {
  section {
    padding: var(--space-3xl) 0;
  }

  .timeline:before {
    left: var(--space-lg);
  }

  .timeline-item {
    padding-left: var(--space-2xl);
    flex-direction: column;
  }

  .timeline-item:before {
    left: var(--space-lg);
    transform: translateX(-50%);
  }

  .timeline-date {
    text-align: left;
    padding-right: 0;
    padding-bottom: var(--space-sm);
    font-size: var(--text-base);
  }

  .timeline-content {
    padding-left: 0;
  }

  .logos-grid {
    gap: var(--space-lg);
    grid-template-columns: 1fr;
  }

  .aspirations-quote:before {
    font-size: 3rem;
    top: -.5rem;
    left: -1rem;
  }

  .aspirations-quote p {
    font-size: var(--text-lg);
  }

  .email-link {
    font-size: var(--text-lg);
    padding: var(--space-md) var(--space-lg);
  }

  .social-links {
    gap: var(--space-sm);
    flex-direction: column;
  }

  .timeline-item:hover {
    transform: none;
  }

  .achievement-item:hover {
    transform: none;
    box-shadow: 0 2px 8px #0000001a;
  }
}

@media (width <= 480px) {
  section {
    padding: var(--space-2xl) 0;
  }

  .about-section .about-content, .timeline {
    max-width: 100%;
  }

  .timeline-item {
    padding-left: var(--space-xl);
  }

  .timeline-item:before, .timeline:before {
    left: var(--space-sm);
  }

  .interests-list, .achievements-list {
    max-width: 100%;
  }

  .achievement-item {
    padding: var(--space-lg);
    padding-left: var(--space-3xl);
  }

  .aspirations-quote {
    max-width: 100%;
  }

  .aspirations-quote:before {
    font-size: 2.5rem;
    top: -.25rem;
    left: -.5rem;
  }

  .email-link {
    font-size: var(--text-base);
    padding: var(--space-sm) var(--space-md);
    text-align: center;
    display: block;
  }
}

/*# sourceMappingURL=src_styles_components_css_f9ee138c._.single.css.map*/