/* [project]/src/styles/components.css [app-client] (css) */
section {
  padding: var(--space-3xl) 0;
  color: var(--color-dark-grey);
  position: relative;
  overflow: hidden;
}

@media (width >= 640px) {
  section {
    padding: var(--space-4xl) 0;
  }
}

@media (width >= 1024px) {
  section {
    padding: var(--space-5xl) 0;
  }
}

section:before {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: .3;
  width: 60px;
  height: 2px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

section:first-child:before {
  display: none;
}

section.revealed {
  animation: sectionReveal .8s var(--transition-smooth) forwards;
}

@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.about-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #16a0850d 0%, #48c9b014 50%, #fffffff2 100%);
  position: relative;
}

.about-section:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: .2;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.about-section .about-content {
  text-align: center;
  max-width: 900px;
  padding: 0 var(--space-md);
  margin: 0 auto;
  position: relative;
}

@media (width >= 640px) {
  .about-section .about-content {
    padding: 0 var(--space-lg);
  }
}

@media (width >= 1024px) {
  .about-section .about-content {
    padding: 0;
  }
}

.about-section .about-content:before {
  content: "";
  background: radial-gradient(circle, var(--color-copper) 2px, transparent 2px);
  opacity: .3;
  width: 40px;
  height: 40px;
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.about-section p {
  font-size: var(--text-base);
  margin-bottom: var(--space-lg);
  color: var(--color-dark-grey);
  padding: 0;
  line-height: 1.7;
  position: relative;
}

.about-section p:first-child {
  font-size: var(--text-lg);
  color: var(--color-black);
  margin-bottom: var(--space-xl);
  font-weight: 500;
}

@media (width >= 640px) {
  .about-section p {
    font-size: var(--text-lg);
    margin-bottom: var(--space-xl);
    line-height: 1.8;
  }

  .about-section p:first-child {
    font-size: var(--text-xl);
    margin-bottom: var(--space-2xl);
  }
}

.about-section p:last-child {
  margin-bottom: 0;
}

.about-section p.revealed {
  animation: textReveal .8s var(--transition-smooth) forwards;
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.academic-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #8e44ad0f 0%, #bb8fce14 50%, #fffffff2 100%);
  position: relative;
}

.academic-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 20% 20%, #3350b808 0%, #0000 50%), radial-gradient(circle at 80% 80%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.timeline {
  max-width: 900px;
  padding: var(--space-lg) var(--space-md);
  margin: 0 auto;
  position: relative;
}

@media (width >= 640px) {
  .timeline {
    padding: var(--space-xl) var(--space-lg);
  }
}

@media (width >= 1024px) {
  .timeline {
    padding: var(--space-xl) 0;
  }
}

.timeline:before {
  content: "";
  background: linear-gradient(to bottom, transparent 0%, var(--color-copper) 10%, var(--color-copper) 90%, transparent 100%);
  width: 3px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 10px #3350b84d;
}

.timeline-item {
  margin-bottom: var(--space-2xl);
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  flex-direction: column;
  display: flex;
  position: relative;
  transform: translateX(-30px);
}

@media (width >= 768px) {
  .timeline-item {
    margin-bottom: var(--space-4xl);
    flex-direction: row;
  }
}

.timeline-item.revealed {
  opacity: 1;
  transform: translateX(0);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: "";
  left: 50%;
  top: var(--space-md);
  background: radial-gradient(circle, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  border: 3px solid var(--color-white);
  z-index: 2;
  width: 16px;
  height: 16px;
  transition: all var(--transition-normal);
  border-radius: 50%;
  position: absolute;
  transform: translateX(-50%);
  box-shadow: 0 0 0 3px #3350b833;
}

.timeline-item:hover:before {
  transform: translateX(-50%)scale(1.2);
  box-shadow: 0 0 0 6px #3350b84d;
}

.timeline-date {
  text-align: left;
  padding-right: 0;
  padding-bottom: var(--space-sm);
  color: var(--color-copper);
  font-weight: 600;
  font-size: var(--text-base);
  transition: all var(--transition-normal);
  flex: 1;
  position: relative;
}

@media (width >= 768px) {
  .timeline-date {
    text-align: right;
    padding-right: var(--space-xl);
    font-size: var(--text-lg);
    padding-bottom: 0;
  }
}

.timeline-date:after {
  content: "";
  right: var(--space-lg);
  background: var(--color-copper);
  width: 0;
  height: 2px;
  transition: width var(--transition-normal);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.timeline-item:hover .timeline-date:after {
  width: 20px;
}

.timeline-content {
  background: var(--color-white);
  padding-left: 0;
  padding: var(--space-md);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  margin-top: var(--space-sm);
  border-radius: 8px;
  flex: 1;
  position: relative;
}

@media (width >= 768px) {
  .timeline-content {
    padding-left: var(--space-xl);
    padding: var(--space-lg);
    border-radius: 12px;
    margin-top: 0;
  }
}

.timeline-content:before {
  content: "";
  left: -8px;
  top: var(--space-lg);
  border-top: 8px solid #0000;
  border-bottom: 8px solid #0000;
  border-right: 8px solid var(--color-white);
  width: 0;
  height: 0;
  position: absolute;
}

.timeline-item:hover .timeline-content {
  box-shadow: var(--shadow-lg);
  transform: translateX(10px);
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  position: relative;
}

.timeline-content h3:after {
  content: "";
  background: var(--color-copper);
  width: 0;
  height: 2px;
  transition: width var(--transition-normal);
  position: absolute;
  bottom: -4px;
  left: 0;
}

.timeline-item:hover .timeline-content h3:after {
  width: 40px;
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-dark-grey);
  margin-bottom: 0;
  line-height: 1.6;
}

.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  border-left: 4px solid var(--color-copper);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

.research-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #e67e220f 0%, #f39c1214 50%, #fffffff2 100%);
  position: relative;
}

.research-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 30% 40%, #3350b80d 0%, #0000 50%), radial-gradient(circle at 70% 60%, #3350b80d 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.interests-list {
  gap: var(--space-md);
  max-width: 800px;
  padding: 0 var(--space-md);
  margin: 0 auto;
  list-style: none;
  display: grid;
}

@media (width >= 640px) {
  .interests-list {
    gap: var(--space-lg);
    padding: 0 var(--space-lg);
  }
}

@media (width >= 1024px) {
  .interests-list {
    padding: 0;
  }
}

.interest-item {
  padding: var(--space-lg);
  background: var(--color-white);
  border-left: 3px solid var(--color-orange);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  color: var(--color-dark-grey);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transform: translateY(30px);
}

@media (width >= 640px) {
  .interest-item {
    padding: var(--space-xl);
    border-left-width: 4px;
    border-radius: 12px;
  }
}

.interest-item.revealed {
  opacity: 1;
  transform: translateY(0);
}

.interest-item:before {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b805 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.interest-item:hover:before {
  opacity: 1;
}

.interest-item:hover {
  box-shadow: var(--shadow-xl);
  border-left-color: var(--color-orange-dark);
  transform: translateX(12px)translateY(-4px);
}

.interest-item:last-child {
  margin-bottom: 0;
}

.interest-item:after {
  content: "→";
  right: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-orange);
  font-size: var(--text-xl);
  opacity: 0;
  transition: all var(--transition-normal);
  position: absolute;
  transform: translateX(-10px);
}

.interest-item:hover:after {
  opacity: 1;
  transform: translateX(0);
}

.achievements-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #27ae600f 0%, #58d68d14 50%, #fffffff2 100%);
  position: relative;
}

.achievements-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 25% 25%, #3350b808 0%, #0000 50%), radial-gradient(circle at 75% 75%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.achievements-list {
  gap: var(--space-lg);
  max-width: 900px;
  padding: 0 var(--space-md);
  margin: 0 auto;
  list-style: none;
  display: grid;
}

@media (width >= 640px) {
  .achievements-list {
    gap: var(--space-xl);
    padding: 0 var(--space-lg);
  }
}

@media (width >= 1024px) {
  .achievements-list {
    padding: 0;
  }
}

.achievement-item {
  padding: var(--space-lg);
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  opacity: 0;
  border: 1px solid #3350b81a;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transform: translateY(30px)scale(.95);
}

@media (width >= 640px) {
  .achievement-item {
    padding: var(--space-xl);
    border-radius: 16px;
  }
}

.achievement-item.revealed {
  opacity: 1;
  transform: translateY(0)scale(1);
}

.achievement-item:before {
  content: "★";
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-emerald);
  font-size: var(--text-2xl);
  transition: all var(--transition-normal);
  z-index: 2;
  position: absolute;
}

.achievement-item:after {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b80d 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.achievement-item:hover:after {
  opacity: 1;
}

.achievement-item {
  padding-left: var(--space-5xl);
}

.achievement-item:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
  transform: translateY(-8px)scale(1.02);
}

.achievement-item:hover:before {
  color: var(--color-emerald-dark);
  text-shadow: 0 0 10px #27ae6080;
  transform: scale(1.2)rotate(72deg);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.achievement-item h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  z-index: 2;
  position: relative;
}

.achievement-item p {
  color: var(--color-dark-grey);
  z-index: 2;
  line-height: 1.6;
  position: relative;
}

.affiliations-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #3350b80f 0%, #749ed414 50%, #fffffff2 100%);
  position: relative;
}

.affiliations-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 40% 30%, #3350b80a 0%, #0000 50%), radial-gradient(circle at 60% 70%, #3350b80a 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-3xl);
  position: relative;
}

.affiliations-subtitle:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  width: 60px;
  height: 2px;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.logos-grid {
  gap: var(--space-2xl);
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  align-items: center;
  max-width: 700px;
  margin: 0 auto;
  display: grid;
}

.logo-item {
  padding: var(--space-xl);
  background: var(--color-white);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  border: 1px solid #3350b81a;
  border-radius: 16px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
  overflow: hidden;
  transform: translateY(30px)scale(.9);
}

.logo-item.revealed {
  opacity: 1;
  transform: translateY(0)scale(1);
}

.logo-item:before {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b80d 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.logo-item:hover:before {
  opacity: 1;
}

.logo-item:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
  transform: translateY(-8px)scale(1.05);
}

.institution-logo {
  filter: grayscale();
  opacity: .7;
  transition: all var(--transition-normal);
  z-index: 2;
  max-width: 100%;
  height: auto;
  position: relative;
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  font-style: italic;
  position: relative;
}

.aspirations-quote:before {
  content: "\"";
  color: var(--color-copper);
  font-size: 4rem;
  font-family: var(--font-serif);
  position: absolute;
  top: -1rem;
  left: -2rem;
}

.aspirations-quote p {
  font-size: var(--text-xl);
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
  line-height: 1.8;
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

.contact-section {
  color: var(--color-dark-grey);
  background: linear-gradient(135deg, #16a0850f 0%, #48c9b014 50%, #fffffff2 100%);
  position: relative;
}

.contact-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 50% 20%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.contact-description {
  text-align: center;
  max-width: 700px;
  margin: 0 auto var(--space-2xl);
  color: var(--color-dark-grey);
  font-size: var(--text-base);
  padding: 0 var(--space-md);
  line-height: 1.6;
  position: relative;
}

@media (width >= 640px) {
  .contact-description {
    margin-bottom: var(--space-3xl);
    font-size: var(--text-lg);
    padding: 0 var(--space-lg);
    line-height: 1.7;
  }
}

@media (width >= 1024px) {
  .contact-description {
    padding: 0;
  }
}

.contact-description:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  width: 80px;
  height: 2px;
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.contact-info {
  text-align: center;
  position: relative;
}

.email-link {
  font-size: var(--text-lg);
  padding: var(--space-md) var(--space-xl);
  min-height: var(--touch-target);
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  margin-bottom: var(--space-lg);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  border-radius: 25px;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

@media (width >= 640px) {
  .email-link {
    font-size: var(--text-xl);
    padding: var(--space-lg) var(--space-2xl);
    margin-bottom: var(--space-xl);
    border-radius: 50px;
    display: inline-block;
  }
}

.email-link:before {
  content: "";
  width: 100%;
  height: 100%;
  transition: left var(--duration-slow);
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  position: absolute;
  top: 0;
  left: -100%;
}

.email-link:hover:before {
  left: 100%;
}

.email-link:hover {
  color: var(--color-white);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, var(--color-copper-dark) 0%, var(--color-copper) 100%);
  transform: translateY(-4px)scale(1.05);
}

.social-links {
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
  display: flex;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

.hero-content {
  animation: 1.5s ease-out heroFadeIn;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: 1s ease-out .3s both slideInFromLeft;
}

.hero-title {
  animation: 1s ease-out .6s both slideInFromLeft;
}

.hero-tagline {
  animation: 1s ease-out .9s both slideInFromLeft;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

body {
  animation: .6s ease-out pageLoad;
}

.section-title {
  position: relative;
  overflow: hidden;
}

.section-title:before {
  content: "";
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
  width: 0;
  height: 3px;
  transition: width var(--duration-slow) var(--transition-smooth);
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.section-title.revealed:before {
  width: 60px;
}

:focus {
  outline: 2px solid var(--color-copper);
  outline-offset: 2px;
  border-radius: 4px;
}

.loading-screen {
  background: linear-gradient(135deg, var(--color-dark-grey) 0%, var(--color-black) 100%);
  z-index: 10000;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  animation: .5s ease-out 2.5s forwards fadeOut;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

.loading-content {
  text-align: center;
  color: var(--color-white);
  z-index: 2;
  position: relative;
}

.loading-logo {
  margin-bottom: var(--space-2xl);
}

.logo-circle {
  border: 3px solid var(--color-copper);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  animation: 2s linear infinite rotate;
  display: flex;
  position: relative;
}

.logo-circle:before {
  content: "";
  border: 3px solid #0000;
  border-top-color: var(--color-copper-light);
  border-radius: 50%;
  animation: 1s linear infinite reverse rotate;
  position: absolute;
  inset: -3px;
}

.logo-inner {
  font-family: var(--font-hero);
  font-size: var(--text-3xl);
  color: var(--color-copper);
  font-weight: 300;
  animation: 2s ease-in-out infinite pulse;
}

.loading-text h2 {
  font-family: var(--font-hero);
  font-size: var(--text-2xl);
  margin-bottom: var(--space-sm);
  color: var(--color-white);
  animation: .8s ease-out .3s both fadeInUp;
}

.loading-text p {
  font-size: var(--text-lg);
  color: var(--color-copper-light);
  margin-bottom: var(--space-2xl);
  animation: .8s ease-out .6s both fadeInUp;
}

.loading-progress {
  width: 300px;
  margin: 0 auto;
  animation: .8s ease-out .9s both fadeInUp;
}

.progress-bar {
  width: 100%;
  height: 4px;
  margin-bottom: var(--space-sm);
  background: #3350b833;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-light));
  border-radius: 2px;
  height: 100%;
  transition: width .3s ease-out;
  position: relative;
}

.progress-fill:after {
  content: "";
  background: linear-gradient(90deg, #0000, #ffffff4d, #0000);
  animation: 1.5s infinite shimmer;
  position: absolute;
  inset: 0;
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--color-copper-light);
  font-weight: 500;
}

.loading-particles {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.particle {
  background: var(--color-copper);
  opacity: .6;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  position: absolute;
}

@media print {
  .hero-section {
    page-break-inside: avoid;
    height: auto;
    min-height: auto;
  }

  .hero-overlay, .hero-background:before {
    display: none;
  }

  section {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .loading-screen {
    display: none;
  }
}

@media (width <= 767px) {
  .interest-item, .achievement-item, .logo-item {
    min-height: var(--touch-target);
  }

  .timeline-content h3 {
    font-size: var(--text-lg);
    line-height: 1.3;
  }

  .timeline-content p {
    font-size: var(--text-sm);
    line-height: 1.5;
  }

  .achievement-item h3 {
    font-size: var(--text-lg);
    line-height: 1.3;
  }

  .achievement-item p {
    font-size: var(--text-sm);
    line-height: 1.5;
  }

  .interest-item, .achievement-item {
    line-height: 1.5;
  }

  .hero-content > * + * {
    margin-top: var(--space-md);
  }

  .section-title {
    margin-bottom: var(--space-lg);
    line-height: 1.2;
  }
}

@media (width <= 767px) and (orientation: landscape) {
  .hero-section {
    min-height: 400px;
  }

  section {
    padding: var(--space-xl) 0;
  }
}

@media (width <= 767px) and (-webkit-device-pixel-ratio >= 2) {
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.about-section, .academic-section, .research-section, .achievements-section, .affiliations-section, .contact-section, .about-section h2, .about-section h3, .about-section p, .academic-section h2, .academic-section h3, .academic-section p, .research-section h2, .research-section h3, .research-section p, .achievements-section h2, .achievements-section h3, .achievements-section p, .affiliations-section h2, .affiliations-section h3, .affiliations-section p, .contact-section h2, .contact-section h3, .contact-section p {
  color: var(--color-dark-grey);
}

.about-section .section-title {
  color: var(--color-teal-dark) !important;
}

.academic-section .section-title {
  color: var(--color-purple-dark) !important;
}

.research-section .section-title {
  color: var(--color-orange-dark) !important;
}

.achievements-section .section-title {
  color: var(--color-emerald-dark) !important;
}

.affiliations-section .section-title {
  color: var(--color-copper-dark) !important;
}

.contact-section .section-title {
  color: var(--color-teal-dark) !important;
}

.section-title {
  color: var(--color-black) !important;
}

.timeline-date {
  color: var(--color-copper) !important;
}

.timeline-content h3 {
  color: var(--color-black) !important;
}

.timeline-content p, .interest-item, .interest-item h3, .interest-item p {
  color: var(--color-dark-grey) !important;
}

.achievement-item h3 {
  color: var(--color-black) !important;
}

.achievement-item p, .contact-description {
  color: var(--color-dark-grey) !important;
}

.aspirations-section, .aspirations-section h2, .aspirations-section h3, .aspirations-section p, .aspirations-section .section-title {
  color: var(--color-white) !important;
}

.interests-list li, .achievements-list li {
  color: var(--color-dark-grey);
}

section:not(.aspirations-section):not(.hero-section) h1, section:not(.aspirations-section):not(.hero-section) h2, section:not(.aspirations-section):not(.hero-section) h3, section:not(.aspirations-section):not(.hero-section) h4, section:not(.aspirations-section):not(.hero-section) h5, section:not(.aspirations-section):not(.hero-section) h6 {
  color: var(--color-black);
}

section:not(.aspirations-section):not(.hero-section) p {
  color: var(--color-dark-grey);
}

.hero-section, .hero-section h1, .hero-section h2, .hero-section p, .hero-name, .hero-title, .hero-tagline {
  color: var(--color-white) !important;
}

.interests-list .interest-item:first-child {
  animation-delay: .1s;
}

.interests-list .interest-item:nth-child(2) {
  animation-delay: .2s;
}

.interests-list .interest-item:nth-child(3) {
  animation-delay: .3s;
}

.interests-list .interest-item:nth-child(4) {
  animation-delay: .4s;
}

.interests-list .interest-item:nth-child(5) {
  animation-delay: .5s;
}

.achievements-list .achievement-item:first-child {
  animation-delay: .1s;
}

.achievements-list .achievement-item:nth-child(2) {
  animation-delay: .25s;
}

.achievements-list .achievement-item:nth-child(3) {
  animation-delay: .4s;
}

.achievements-list .achievement-item:nth-child(4) {
  animation-delay: .55s;
}

@media (width >= 640px) {
  .logos-grid {
    gap: var(--space-xl);
    grid-template-columns: repeat(2, 1fr);
  }

  .loading-progress {
    width: 300px;
  }

  .logo-circle {
    width: 100px;
    height: 100px;
  }

  .logo-inner {
    font-size: var(--text-2xl);
  }
}

@media (width >= 768px) {
  .timeline:before, .timeline-item:before {
    left: 50%;
  }

  .timeline-date:after {
    right: var(--space-lg);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

@media (width >= 1024px) {
  .logos-grid {
    gap: var(--space-2xl);
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .aspirations-quote {
    max-width: 900px;
  }

  .loading-progress {
    width: 300px;
  }

  .logo-circle {
    width: 120px;
    height: 120px;
  }

  .logo-inner {
    font-size: var(--text-3xl);
  }
}

@media (width <= 767px) {
  .timeline:before {
    left: var(--space-md);
  }

  .timeline-item:before {
    left: var(--space-md);
    transform: translateX(-50%);
  }

  .timeline-content:before {
    display: none;
  }

  .logos-grid {
    gap: var(--space-lg);
    grid-template-columns: 1fr;
  }

  .aspirations-quote:before {
    font-size: 2.5rem;
    top: -.25rem;
    left: -.5rem;
  }

  .aspirations-quote p {
    font-size: var(--text-base);
  }

  .social-links {
    gap: var(--space-sm);
    flex-direction: column;
  }

  .timeline-item:hover {
    transform: none;
  }

  .timeline-item:hover .timeline-content, .achievement-item:hover, .interest-item:hover {
    box-shadow: var(--shadow-md);
    transform: none;
  }

  .logo-item:hover {
    transform: none;
  }

  .interest-item:hover:after {
    display: none;
  }

  .achievement-item:hover:before {
    transform: none;
  }
}

@media (width <= 479px) {
  section {
    padding: var(--space-2xl) 0;
  }

  .timeline:before, .timeline-item:before {
    left: var(--space-sm);
  }

  .achievement-item {
    padding-left: var(--space-3xl);
  }

  .loading-text h2 {
    font-size: var(--text-xl);
  }

  .loading-progress {
    width: 250px;
  }

  .logo-circle {
    width: 80px;
    height: 80px;
  }

  .logo-inner {
    font-size: var(--text-xl);
  }

  .about-section .about-content, .timeline, .interests-list, .achievements-list, .aspirations-quote {
    max-width: 100%;
  }
}

/*# sourceMappingURL=src_styles_components_css_f9ee138c._.single.css.map*/