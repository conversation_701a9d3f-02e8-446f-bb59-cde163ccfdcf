{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function ScrollAnimations() {\n  useEffect(() => {\n    // Intersection Observer for fade-in animations\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    // Add fade-in class to all sections and observe them\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      section.classList.add('fade-in');\n      section.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Add fade-in to timeline items\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to interest items\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to achievement items\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to logo items\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Optimized parallax effect for hero section\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            const rate = scrolled * 0.3;\n            heroImage.style.transform = `translate3d(0, ${rate}px, 0)`;\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,+CAA+C;YAC/C,MAAM,kBAAkB;gBACtB,WAAW;gBACX,YAAY;YACd;YAEA,MAAM,WAAW,IAAI;8CAAqB,CAAC;oBACzC,QAAQ,OAAO;sDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;4BAC7B;wBACF;;gBACF;6CAAG;YAEH,qDAAqD;YACrD,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO;8CAAC,CAAC,SAAS;oBACzB,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACtB,QAAQ,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBACjD,SAAS,OAAO,CAAC;gBACnB;;YAEA,gCAAgC;YAChC,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,KAAK,SAAS,CAAC,GAAG,CAAC;oBACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAC9C,SAAS,OAAO,CAAC;gBACnB;;YAEA,gCAAgC;YAChC,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,KAAK,SAAS,CAAC,GAAG,CAAC;oBACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAC9C,SAAS,OAAO,CAAC;gBACnB;;YAEA,mCAAmC;YACnC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO;8CAAC,CAAC,MAAM;oBAC9B,KAAK,SAAS,CAAC,GAAG,CAAC;oBACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;oBAC/C,SAAS,OAAO,CAAC;gBACnB;;YAEA,4BAA4B;YAC5B,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,UAAU,OAAO;8CAAC,CAAC,MAAM;oBACvB,KAAK,SAAS,CAAC,GAAG,CAAC;oBACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAC9C,SAAS,OAAO,CAAC;gBACnB;;YAEA,kDAAkD;YAClD,MAAM;iEAAqB,CAAC;oBAC1B,MAAM,SAAS,EAAE,MAAM;oBACvB,IAAI,OAAO,IAAI,EAAE;wBACf,EAAE,cAAc;wBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;wBAClD,IAAI,SAAS;4BACX,QAAQ,cAAc,CAAC;gCACrB,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;;YAEA,wCAAwC;YACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;YAC9C,YAAY,OAAO;8CAAC,CAAA;oBAClB,KAAK,gBAAgB,CAAC,SAAS;gBACjC;;YAEA,6CAA6C;YAC7C,IAAI,UAAU;YACd,MAAM;6DAAiB;oBACrB,IAAI,CAAC,SAAS;wBACZ;yEAAsB;gCACpB,MAAM,WAAW,OAAO,WAAW;gCACnC,MAAM,cAAc,SAAS,aAAa,CAAC;gCAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;gCAEzC,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;oCAC7D,MAAM,OAAO,WAAW;oCACxB,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,KAAK,MAAM,CAAC;gCAC5D;gCACA,UAAU;4BACZ;;wBACA,UAAU;oBACZ;gBACF;;YAEA,yCAAyC;YACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;gBAAE,SAAS;YAAK;YAElE,mBAAmB;YACnB;8CAAO;oBACL,SAAS,UAAU;oBACnB,YAAY,OAAO;sDAAC,CAAA;4BAClB,KAAK,mBAAmB,CAAC,SAAS;wBACpC;;oBACA,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;qCAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;GA9GwB;KAAA", "debugId": null}}]}