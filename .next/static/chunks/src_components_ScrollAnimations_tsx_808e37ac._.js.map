{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function ScrollAnimations() {\n  // Performance-optimized observer callback\n  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {\n    entries.forEach((entry) => {\n      const element = entry.target as HTMLElement;\n      const intersectionRatio = entry.intersectionRatio;\n\n      if (entry.isIntersecting) {\n        // Add visible class for basic animations\n        element.classList.add('visible', 'revealed');\n\n        // Add progressive reveal based on intersection ratio\n        if (intersectionRatio > 0.5) {\n          element.classList.add('fully-visible');\n        }\n\n        // Trigger staggered animations for child elements\n        const children = element.querySelectorAll('.stagger-child');\n        children.forEach((child, index) => {\n          setTimeout(() => {\n            (child as HTMLElement).classList.add('revealed');\n          }, index * 100);\n        });\n      } else {\n        // Optional: Remove classes for re-animation on scroll up\n        if (intersectionRatio === 0) {\n          element.classList.remove('fully-visible');\n        }\n      }\n    });\n  }, []);\n\n  useEffect(() => {\n    // Enhanced Intersection Observer for sophisticated animations\n    const observerOptions = {\n      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver(observerCallback, observerOptions);\n\n    // Enhanced section animations with different reveal types\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      const htmlSection = section as HTMLElement;\n\n      // Alternate animation types for visual variety\n      const animationType = index % 3;\n      switch (animationType) {\n        case 0:\n          htmlSection.classList.add('reveal');\n          break;\n        case 1:\n          htmlSection.classList.add('reveal-scale');\n          break;\n        case 2:\n          htmlSection.classList.add('reveal-slide-left');\n          break;\n      }\n\n      htmlSection.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Enhanced timeline items with staggered reveals\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-slide-right');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n\n      // Add stagger-child class to timeline content\n      const content = htmlItem.querySelector('.timeline-content');\n      if (content) {\n        content.classList.add('stagger-child');\n      }\n\n      observer.observe(item);\n    });\n\n    // Enhanced interest items with hover effects\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced achievement items with scale animations\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced logo items with glow effects\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-glow');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Enhanced parallax effect with multiple layers\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n          const heroOverlay = document.querySelector('.hero-overlay') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            // Parallax for hero image\n            const imageRate = scrolled * 0.3;\n            heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.1)`;\n\n            // Subtle parallax for overlay\n            if (heroOverlay) {\n              const overlayRate = scrolled * 0.1;\n              heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;\n            }\n\n            // Fade out hero content as user scrolls\n            const heroContent = document.querySelector('.hero-content') as HTMLElement;\n            if (heroContent) {\n              const fadeRate = Math.max(0, 1 - (scrolled / (window.innerHeight * 0.8)));\n              heroContent.style.opacity = fadeRate.toString();\n              heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;\n            }\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, [observerCallback]);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,QAAQ,OAAO;kEAAC,CAAC;oBACf,MAAM,UAAU,MAAM,MAAM;oBAC5B,MAAM,oBAAoB,MAAM,iBAAiB;oBAEjD,IAAI,MAAM,cAAc,EAAE;wBACxB,yCAAyC;wBACzC,QAAQ,SAAS,CAAC,GAAG,CAAC,WAAW;wBAEjC,qDAAqD;wBACrD,IAAI,oBAAoB,KAAK;4BAC3B,QAAQ,SAAS,CAAC,GAAG,CAAC;wBACxB;wBAEA,kDAAkD;wBAClD,MAAM,WAAW,QAAQ,gBAAgB,CAAC;wBAC1C,SAAS,OAAO;8EAAC,CAAC,OAAO;gCACvB;sFAAW;wCACR,MAAsB,SAAS,CAAC,GAAG,CAAC;oCACvC;qFAAG,QAAQ;4BACb;;oBACF,OAAO;wBACL,yDAAyD;wBACzD,IAAI,sBAAsB,GAAG;4BAC3B,QAAQ,SAAS,CAAC,MAAM,CAAC;wBAC3B;oBACF;gBACF;;QACF;yDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,8DAA8D;YAC9D,MAAM,kBAAkB;gBACtB,WAAW;oBAAC;oBAAG;oBAAK;oBAAM;oBAAK;oBAAM;iBAAE;gBACvC,YAAY;YACd;YAEA,MAAM,WAAW,IAAI,qBAAqB,kBAAkB;YAE5D,0DAA0D;YAC1D,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO;8CAAC,CAAC,SAAS;oBACzB,MAAM,cAAc;oBAEpB,+CAA+C;oBAC/C,MAAM,gBAAgB,QAAQ;oBAC9B,OAAQ;wBACN,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;wBACF,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;wBACF,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;oBACJ;oBAEA,YAAY,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBACrD,SAAS,OAAO,CAAC;gBACnB;;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC;oBACvB,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAElD,8CAA8C;oBAC9C,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,IAAI,SAAS;wBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;oBAEA,SAAS,OAAO,CAAC;gBACnB;;YAEA,6CAA6C;YAC7C,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,UAAU;oBACjC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAClD,SAAS,OAAO,CAAC;gBACnB;;YAEA,mDAAmD;YACnD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO;8CAAC,CAAC,MAAM;oBAC9B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;oBACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;oBACnD,SAAS,OAAO,CAAC;gBACnB;;YAEA,wCAAwC;YACxC,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,UAAU,OAAO;8CAAC,CAAC,MAAM;oBACvB,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;oBACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAClD,SAAS,OAAO,CAAC;gBACnB;;YAEA,kDAAkD;YAClD,MAAM;iEAAqB,CAAC;oBAC1B,MAAM,SAAS,EAAE,MAAM;oBACvB,IAAI,OAAO,IAAI,EAAE;wBACf,EAAE,cAAc;wBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;wBAClD,IAAI,SAAS;4BACX,QAAQ,cAAc,CAAC;gCACrB,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;;YAEA,wCAAwC;YACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;YAC9C,YAAY,OAAO;8CAAC,CAAA;oBAClB,KAAK,gBAAgB,CAAC,SAAS;gBACjC;;YAEA,gDAAgD;YAChD,IAAI,UAAU;YACd,MAAM;6DAAiB;oBACrB,IAAI,CAAC,SAAS;wBACZ;yEAAsB;gCACpB,MAAM,WAAW,OAAO,WAAW;gCACnC,MAAM,cAAc,SAAS,aAAa,CAAC;gCAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;gCACzC,MAAM,cAAc,SAAS,aAAa,CAAC;gCAE3C,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;oCAC7D,0BAA0B;oCAC1B,MAAM,YAAY,WAAW;oCAC7B,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,iBAAiB,CAAC;oCAE1E,8BAA8B;oCAC9B,IAAI,aAAa;wCACf,MAAM,cAAc,WAAW;wCAC/B,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,YAAY,MAAM,CAAC;oCACrE;oCAEA,wCAAwC;oCACxC,MAAM,cAAc,SAAS,aAAa,CAAC;oCAC3C,IAAI,aAAa;wCACf,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,IAAK,WAAW,CAAC,OAAO,WAAW,GAAG,GAAG;wCACtE,YAAY,KAAK,CAAC,OAAO,GAAG,SAAS,QAAQ;wCAC7C,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,IAAI,GAAG,CAAC;oCACjE;gCACF;gCACA,UAAU;4BACZ;;wBACA,UAAU;oBACZ;gBACF;;YAEA,yCAAyC;YACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;gBAAE,SAAS;YAAK;YAElE,mBAAmB;YACnB;8CAAO;oBACL,SAAS,UAAU;oBACnB,YAAY,OAAO;sDAAC,CAAA;4BAClB,KAAK,mBAAmB,CAAC,SAAS;wBACpC;;oBACA,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;qCAAG;QAAC;KAAiB;IAErB,OAAO,MAAM,yCAAyC;AACxD;GAjLwB;KAAA", "debugId": null}}]}