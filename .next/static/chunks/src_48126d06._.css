/* [project]/src/app/globals.css [app-client] (css) */
:root {
  --color-black: #0a0a0a;
  --color-dark-grey: #0a1322;
  --color-medium-grey: #4a4a4a;
  --color-light-grey: #8a8a8a;
  --color-very-light-grey: #e5e5e5;
  --color-white: #fff;
  --color-copper: #3350b8;
  --color-copper-light: #749ed4;
  --color-copper-dark: #2b458b;
  --font-serif: "Playfair Display", Georgia, serif;
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-hero: "Old Standard TT", Georgia, serif;
  --text-xs: .75rem;
  --text-sm: .875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-8xl: 8.75rem;
  --space-xs: .5rem;
  --space-sm: .75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
  --container-max-width: 1200px;
  --container-padding: var(--space-lg);
  --transition-fast: .15s ease;
  --transition-normal: .3s ease;
  --transition-slow: .5s ease;
  --transition-bounce: .4s cubic-bezier(.68, -.55, .265, 1.55);
  --transition-smooth: .6s cubic-bezier(.4, 0, .2, 1);
  --transition-elastic: .8s cubic-bezier(.175, .885, .32, 1.275);
  --shadow-sm: 0 1px 2px #0000000d;
  --shadow-md: 0 4px 6px #00000012;
  --shadow-lg: 0 10px 15px #0000001a;
  --shadow-xl: 0 20px 25px #00000026;
  --shadow-2xl: 0 25px 50px #00000040;
  --shadow-glow: 0 0 20px #3350b84d;
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);
  --duration-fast: .2s;
  --duration-normal: .4s;
  --duration-slow: .6s;
  --duration-slower: .8s;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  overflow-x: hidden;
}

body {
  font-family: var(--font-sans);
  color: var(--color-dark-grey);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.6;
  position: relative;
  overflow-x: hidden;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-very-light-grey);
}

::-webkit-scrollbar-thumb {
  background: var(--color-copper);
  transition: background var(--transition-fast);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-copper-dark);
}

* {
  will-change: auto;
}

.hero-image, .timeline-item, .interest-item, .achievement-item, .logo-item {
  will-change: transform;
}

@media (prefers-reduced-motion: reduce) {
  *, :before, :after {
    scroll-behavior: auto !important;
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }

  .hero-image {
    transform: none !important;
  }

  .floating-shape {
    animation: none !important;
  }
}

@media (prefers-contrast: high) {
  :root {
    --color-copper: #06c;
    --color-copper-light: #39f;
    --color-copper-dark: #003d7a;
  }

  .hero-overlay {
    background: #000c;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-white: #1a1a1a;
    --color-very-light-grey: #2a2a2a;
    --color-light-grey: #4a4a4a;
    --color-medium-grey: #8a8a8a;
    --color-dark-grey: #e5e5e5;
    --color-black: #fff;
  }
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  margin-bottom: var(--space-md);
  color: var(--color-black);
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 600;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-lg);
  font-size: var(--text-lg);
  line-height: 1.7;
}

a {
  color: var(--color-copper);
  transition: color var(--transition-fast);
  text-decoration: none;
}

a:hover {
  color: var(--color-copper-dark);
}

.container {
  max-width: var(--container-max-width);
  padding: 0 var(--container-padding);
  margin: 0 auto;
}

.section-title {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-2xl);
  text-align: center;
  color: var(--color-black);
  font-weight: 600;
}

.fade-in {
  opacity: 0;
  transition: opacity var(--transition-slow), transform var(--transition-slow);
  transform: translateY(20px);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-up {
  animation: fadeInUp var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--transition-bounce) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-float {
  animation: 3s ease-in-out infinite float;
}

.animate-pulse {
  animation: 2s ease-in-out infinite pulse;
}

.animate-glow {
  animation: 2s ease-in-out infinite glow;
}

.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-8px);
}

.hover-scale {
  transition: transform var(--transition-bounce);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--color-very-light-grey) 0%, var(--color-white) 50%, var(--color-very-light-grey) 100%);
  background-size: 200% 100%;
  animation: 1.5s infinite shimmer;
}

.reveal {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateY(50px);
}

.reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-scale {
  opacity: 0;
  transition: all var(--duration-normal) var(--transition-bounce);
  transform: scale(.8);
}

.reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

.reveal-slide-left {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateX(-50px);
}

.reveal-slide-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-slide-right {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateX(50px);
}

.reveal-slide-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-copper);
  }

  50% {
    box-shadow: 0 0 20px var(--color-copper), 0 0 30px var(--color-copper-light);
  }
}

.hero-section {
  background: var(--color-black);
  justify-content: center;
  align-items: center;
  height: 100vh;
  min-height: 600px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.hero-background {
  z-index: 1;
  will-change: transform;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-image {
  object-fit: cover;
  object-position: top;
  transition: transform var(--duration-slower) ease-out;
  will-change: transform;
}

.hero-overlay {
  z-index: 2;
  background: linear-gradient(135deg, #000000b3 0%, #15296080 50%, #000c 100%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-overlay:before {
  content: "";
  background: radial-gradient(circle at 20% 80%, #3350b81a 0%, #0000 50%), radial-gradient(circle at 80% 20%, #3350b81a 0%, #0000 50%);
  width: 100%;
  height: 100%;
  animation: 6s ease-in-out infinite float;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-content {
  z-index: 3;
  text-align: center;
  color: var(--color-white);
  max-width: 900px;
  padding: 0 var(--space-lg);
  animation: heroContentReveal 1.2s var(--transition-smooth) forwards;
  position: relative;
}

@keyframes heroContentReveal {
  0% {
    opacity: 0;
    transform: translateY(40px)scale(.95);
  }

  100% {
    opacity: 1;
    transform: translateY(0)scale(1);
  }
}

.hero-name {
  font-family: var(--font-hero);
  font-size: var(--text-8xl);
  margin-bottom: var(--space-md);
  text-shadow: 2px 2px 4px #00000080, 0 0 20px #3350b84d;
  color: var(--color-white);
  letter-spacing: -.02em;
  opacity: 0;
  animation: heroNameReveal 1s var(--transition-smooth) .3s forwards;
  font-style: italic;
  font-weight: 100;
  position: relative;
}

@keyframes heroNameReveal {
  0% {
    opacity: 0;
    filter: blur(10px);
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    filter: blur();
    transform: translateY(0);
  }
}

.hero-name:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  width: 0;
  height: 2px;
  animation: 1s ease-out 1.3s forwards underlineExpand;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

@keyframes underlineExpand {
  0% {
    width: 0;
  }

  100% {
    width: 60%;
  }
}

.hero-title {
  font-size: var(--text-2xl);
  font-weight: 400;
  font-family: var(--font-sans);
  margin-bottom: var(--space-lg);
  color: var(--color-copper-light);
  opacity: 0;
  animation: heroTitleReveal 1s var(--transition-smooth) .6s forwards;
  text-shadow: 1px 1px 2px #0000004d;
}

@keyframes heroTitleReveal {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-tagline {
  font-size: var(--text-xl);
  opacity: 0;
  animation: heroTaglineReveal 1s var(--transition-smooth) .9s forwards;
  text-shadow: 1px 1px 2px #0000004d;
  margin-bottom: 0;
  font-style: italic;
  font-weight: 300;
}

@keyframes heroTaglineReveal {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: .9;
    transform: translateX(0);
  }
}

@media (width <= 1200px) {
  :root {
    --container-padding: var(--space-lg);
  }
}

@media (width <= 768px) {
  :root {
    --text-5xl: 2.5rem;
    --text-4xl: 2rem;
    --text-3xl: 1.5rem;
    --text-6xl: 3rem;
    --container-padding: var(--space-md);
    --space-5xl: 4rem;
    --space-4xl: 3rem;
    --space-3xl: 2rem;
  }

  h1 {
    font-size: var(--text-4xl);
  }

  h2 {
    font-size: var(--text-3xl);
  }

  p {
    font-size: var(--text-base);
  }

  .hero-name {
    font-size: var(--text-5xl);
  }

  .hero-title {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 500px;
  }
}

@media (width <= 480px) {
  :root {
    --text-6xl: 2.5rem;
    --text-5xl: 2rem;
    --text-4xl: 1.75rem;
    --text-3xl: 1.25rem;
    --container-padding: var(--space-sm);
    --space-5xl: 3rem;
    --space-4xl: 2rem;
    --space-3xl: 1.5rem;
  }

  .hero-name {
    font-size: var(--text-5xl);
    line-height: 1.1;
  }

  .hero-title {
    font-size: var(--text-lg);
  }

  .hero-tagline {
    font-size: var(--text-base);
  }

  .section-title {
    font-size: var(--text-3xl);
  }
}


/* [project]/src/styles/components.css [app-client] (css) */
section {
  padding: var(--space-5xl) 0;
  position: relative;
  overflow: hidden;
}

section:nth-child(2n) {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

section:nth-child(odd) {
  background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
}

section:before {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: .3;
  width: 60px;
  height: 2px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

section:first-child:before {
  display: none;
}

section.revealed {
  animation: sectionReveal .8s var(--transition-smooth) forwards;
}

@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.about-section {
  position: relative;
}

.about-section:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  opacity: .2;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.about-section .about-content {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
}

.about-section .about-content:before {
  content: "";
  background: radial-gradient(circle, var(--color-copper) 2px, transparent 2px);
  opacity: .3;
  width: 40px;
  height: 40px;
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.about-section p {
  font-size: var(--text-lg);
  margin-bottom: var(--space-xl);
  padding: 0 var(--space-lg);
  line-height: 1.8;
  position: relative;
}

.about-section p:first-child {
  font-size: var(--text-xl);
  color: var(--color-black);
  margin-bottom: var(--space-2xl);
  font-weight: 500;
}

.about-section p:last-child {
  margin-bottom: 0;
}

.about-section p.revealed {
  animation: textReveal .8s var(--transition-smooth) forwards;
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.academic-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.academic-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 20% 20%, #3350b808 0%, #0000 50%), radial-gradient(circle at 80% 80%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.timeline {
  max-width: 900px;
  padding: var(--space-xl) 0;
  margin: 0 auto;
  position: relative;
}

.timeline:before {
  content: "";
  background: linear-gradient(to bottom, transparent 0%, var(--color-copper) 10%, var(--color-copper) 90%, transparent 100%);
  width: 3px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 10px #3350b84d;
}

.timeline-item {
  margin-bottom: var(--space-4xl);
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  display: flex;
  position: relative;
  transform: translateX(-30px);
}

.timeline-item.revealed {
  opacity: 1;
  transform: translateX(0);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: "";
  left: 50%;
  top: var(--space-md);
  background: radial-gradient(circle, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  border: 3px solid var(--color-white);
  z-index: 2;
  width: 16px;
  height: 16px;
  transition: all var(--transition-normal);
  border-radius: 50%;
  position: absolute;
  transform: translateX(-50%);
  box-shadow: 0 0 0 3px #3350b833;
}

.timeline-item:hover:before {
  transform: translateX(-50%)scale(1.2);
  box-shadow: 0 0 0 6px #3350b84d;
}

.timeline-date {
  text-align: right;
  padding-right: var(--space-xl);
  color: var(--color-copper);
  font-weight: 600;
  font-size: var(--text-lg);
  transition: all var(--transition-normal);
  flex: 1;
  position: relative;
}

.timeline-date:after {
  content: "";
  right: var(--space-lg);
  background: var(--color-copper);
  width: 0;
  height: 2px;
  transition: width var(--transition-normal);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.timeline-item:hover .timeline-date:after {
  width: 20px;
}

.timeline-content {
  padding-left: var(--space-xl);
  background: var(--color-white);
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border-radius: 12px;
  flex: 1;
  position: relative;
}

.timeline-content:before {
  content: "";
  left: -8px;
  top: var(--space-lg);
  border-top: 8px solid #0000;
  border-bottom: 8px solid #0000;
  border-right: 8px solid var(--color-white);
  width: 0;
  height: 0;
  position: absolute;
}

.timeline-item:hover .timeline-content {
  box-shadow: var(--shadow-lg);
  transform: translateX(10px);
}

.timeline-content h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  position: relative;
}

.timeline-content h3:after {
  content: "";
  background: var(--color-copper);
  width: 0;
  height: 2px;
  transition: width var(--transition-normal);
  position: absolute;
  bottom: -4px;
  left: 0;
}

.timeline-item:hover .timeline-content h3:after {
  width: 40px;
}

.timeline-content p {
  font-size: var(--text-base);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  line-height: 1.6;
}

.academic-achievement {
  max-width: 800px;
  margin: var(--space-3xl) auto 0;
  padding: var(--space-xl);
  border-left: 4px solid var(--color-copper);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.academic-achievement h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--color-black);
}

.academic-achievement p {
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: 0;
  font-weight: 500;
}

.research-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.research-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 30% 40%, #3350b80d 0%, #0000 50%), radial-gradient(circle at 70% 60%, #3350b80d 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.interests-list {
  gap: var(--space-lg);
  max-width: 800px;
  margin: 0 auto;
  list-style: none;
  display: grid;
}

.interest-item {
  padding: var(--space-xl);
  background: var(--color-white);
  border-left: 4px solid var(--color-copper);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transform: translateY(30px);
}

.interest-item.revealed {
  opacity: 1;
  transform: translateY(0);
}

.interest-item:before {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b805 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.interest-item:hover:before {
  opacity: 1;
}

.interest-item:hover {
  box-shadow: var(--shadow-xl);
  border-left-color: var(--color-copper-dark);
  transform: translateX(12px)translateY(-4px);
}

.interest-item:last-child {
  margin-bottom: 0;
}

.interest-item:after {
  content: "→";
  right: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-xl);
  opacity: 0;
  transition: all var(--transition-normal);
  position: absolute;
  transform: translateX(-10px);
}

.interest-item:hover:after {
  opacity: 1;
  transform: translateX(0);
}

.achievements-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.achievements-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 25% 25%, #3350b808 0%, #0000 50%), radial-gradient(circle at 75% 75%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.achievements-list {
  gap: var(--space-xl);
  max-width: 900px;
  margin: 0 auto;
  list-style: none;
  display: grid;
}

.achievement-item {
  padding: var(--space-xl);
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  opacity: 0;
  border: 1px solid #3350b81a;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  transform: translateY(30px)scale(.95);
}

.achievement-item.revealed {
  opacity: 1;
  transform: translateY(0)scale(1);
}

.achievement-item:before {
  content: "★";
  left: var(--space-lg);
  top: var(--space-lg);
  color: var(--color-copper);
  font-size: var(--text-2xl);
  transition: all var(--transition-normal);
  z-index: 2;
  position: absolute;
}

.achievement-item:after {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b80d 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.achievement-item:hover:after {
  opacity: 1;
}

.achievement-item {
  padding-left: var(--space-5xl);
}

.achievement-item:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
  transform: translateY(-8px)scale(1.02);
}

.achievement-item:hover:before {
  color: var(--color-copper-dark);
  text-shadow: 0 0 10px #3350b880;
  transform: scale(1.2)rotate(72deg);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.achievement-item h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
  color: var(--color-black);
  z-index: 2;
  position: relative;
}

.achievement-item p {
  color: var(--color-medium-grey);
  z-index: 2;
  line-height: 1.6;
  position: relative;
}

.affiliations-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  position: relative;
}

.affiliations-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 40% 30%, #3350b80a 0%, #0000 50%), radial-gradient(circle at 60% 70%, #3350b80a 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.affiliations-subtitle {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-medium-grey);
  margin-bottom: var(--space-3xl);
  position: relative;
}

.affiliations-subtitle:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  width: 60px;
  height: 2px;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.logos-grid {
  gap: var(--space-2xl);
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  align-items: center;
  max-width: 700px;
  margin: 0 auto;
  display: grid;
}

.logo-item {
  padding: var(--space-xl);
  background: var(--color-white);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  border: 1px solid #3350b81a;
  border-radius: 16px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
  overflow: hidden;
  transform: translateY(30px)scale(.9);
}

.logo-item.revealed {
  opacity: 1;
  transform: translateY(0)scale(1);
}

.logo-item:before {
  content: "";
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity var(--transition-normal);
  background: linear-gradient(135deg, #3350b80d 0%, #0000 50%);
  position: absolute;
  top: 0;
  left: 0;
}

.logo-item:hover:before {
  opacity: 1;
}

.logo-item:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--color-copper);
  transform: translateY(-8px)scale(1.05);
}

.institution-logo {
  filter: grayscale();
  opacity: .7;
  transition: all var(--transition-normal);
  z-index: 2;
  max-width: 100%;
  height: auto;
  position: relative;
}

.logo-item:hover .institution-logo {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

.aspirations-section {
  background-color: var(--color-dark-grey);
  color: var(--color-white);
}

.aspirations-section .section-title {
  color: var(--color-white);
}

.aspirations-quote {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  font-style: italic;
  position: relative;
}

.aspirations-quote:before {
  content: "\"";
  color: var(--color-copper);
  font-size: 4rem;
  font-family: var(--font-serif);
  position: absolute;
  top: -1rem;
  left: -2rem;
}

.aspirations-quote p {
  font-size: var(--text-xl);
  margin-bottom: var(--space-xl);
  color: var(--color-very-light-grey);
  line-height: 1.8;
}

.aspirations-quote p:last-child {
  margin-bottom: 0;
}

.contact-section {
  background: linear-gradient(135deg, var(--color-white) 0%, #fafafa 100%);
  position: relative;
}

.contact-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 50% 20%, #3350b808 0%, #0000 50%);
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.contact-description {
  text-align: center;
  max-width: 700px;
  margin: 0 auto var(--space-3xl);
  color: var(--color-medium-grey);
  font-size: var(--text-lg);
  line-height: 1.7;
  position: relative;
}

.contact-description:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-copper), transparent);
  width: 80px;
  height: 2px;
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.contact-info {
  text-align: center;
  position: relative;
}

.email-link {
  font-size: var(--text-xl);
  padding: var(--space-lg) var(--space-2xl);
  background: linear-gradient(135deg, var(--color-copper) 0%, var(--color-copper-dark) 100%);
  color: var(--color-white);
  margin-bottom: var(--space-xl);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  border-radius: 50px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.email-link:before {
  content: "";
  width: 100%;
  height: 100%;
  transition: left var(--duration-slow);
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  position: absolute;
  top: 0;
  left: -100%;
}

.email-link:hover:before {
  left: 100%;
}

.email-link:hover {
  color: var(--color-white);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, var(--color-copper-dark) 0%, var(--color-copper) 100%);
  transform: translateY(-4px)scale(1.05);
}

.social-links {
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
  display: flex;
}

.social-placeholder {
  color: var(--color-light-grey);
  font-size: var(--text-sm);
}

.social-note {
  color: var(--color-light-grey);
  font-size: var(--text-xs);
  font-style: italic;
}

.hero-content {
  animation: 1.5s ease-out heroFadeIn;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-name {
  animation: 1s ease-out .3s both slideInFromLeft;
}

.hero-title {
  animation: 1s ease-out .6s both slideInFromLeft;
}

.hero-tagline {
  animation: 1s ease-out .9s both slideInFromLeft;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

body {
  animation: .6s ease-out pageLoad;
}

.section-title {
  position: relative;
  overflow: hidden;
}

.section-title:before {
  content: "";
  background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
  width: 0;
  height: 3px;
  transition: width var(--duration-slow) var(--transition-smooth);
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.section-title.revealed:before {
  width: 60px;
}

:focus {
  outline: 2px solid var(--color-copper);
  outline-offset: 2px;
  border-radius: 4px;
}

@media print {
  .hero-section {
    page-break-inside: avoid;
    height: auto;
    min-height: auto;
  }

  .hero-overlay, .hero-background:before {
    display: none;
  }

  section {
    page-break-inside: avoid;
    break-inside: avoid;
  }
}

.interests-list .interest-item:first-child {
  animation-delay: .1s;
}

.interests-list .interest-item:nth-child(2) {
  animation-delay: .2s;
}

.interests-list .interest-item:nth-child(3) {
  animation-delay: .3s;
}

.interests-list .interest-item:nth-child(4) {
  animation-delay: .4s;
}

.interests-list .interest-item:nth-child(5) {
  animation-delay: .5s;
}

.achievements-list .achievement-item:first-child {
  animation-delay: .1s;
}

.achievements-list .achievement-item:nth-child(2) {
  animation-delay: .25s;
}

.achievements-list .achievement-item:nth-child(3) {
  animation-delay: .4s;
}

.achievements-list .achievement-item:nth-child(4) {
  animation-delay: .55s;
}

@media (width <= 1024px) {
  .timeline-item {
    margin-bottom: var(--space-2xl);
  }

  .logos-grid {
    gap: var(--space-xl);
    grid-template-columns: repeat(2, 1fr);
  }

  .aspirations-quote {
    max-width: 700px;
  }
}

@media (width <= 768px) {
  section {
    padding: var(--space-3xl) 0;
  }

  .timeline:before {
    left: var(--space-lg);
  }

  .timeline-item {
    padding-left: var(--space-2xl);
    flex-direction: column;
  }

  .timeline-item:before {
    left: var(--space-lg);
    transform: translateX(-50%);
  }

  .timeline-date {
    text-align: left;
    padding-right: 0;
    padding-bottom: var(--space-sm);
    font-size: var(--text-base);
  }

  .timeline-content {
    padding-left: 0;
  }

  .logos-grid {
    gap: var(--space-lg);
    grid-template-columns: 1fr;
  }

  .aspirations-quote:before {
    font-size: 3rem;
    top: -.5rem;
    left: -1rem;
  }

  .aspirations-quote p {
    font-size: var(--text-lg);
  }

  .email-link {
    font-size: var(--text-lg);
    padding: var(--space-md) var(--space-lg);
  }

  .social-links {
    gap: var(--space-sm);
    flex-direction: column;
  }

  .timeline-item:hover {
    transform: none;
  }

  .achievement-item:hover {
    transform: none;
    box-shadow: 0 2px 8px #0000001a;
  }
}

@media (width <= 480px) {
  section {
    padding: var(--space-2xl) 0;
  }

  .about-section .about-content, .timeline {
    max-width: 100%;
  }

  .timeline-item {
    padding-left: var(--space-xl);
  }

  .timeline-item:before, .timeline:before {
    left: var(--space-sm);
  }

  .interests-list, .achievements-list {
    max-width: 100%;
  }

  .achievement-item {
    padding: var(--space-lg);
    padding-left: var(--space-3xl);
  }

  .aspirations-quote {
    max-width: 100%;
  }

  .aspirations-quote:before {
    font-size: 2.5rem;
    top: -.25rem;
    left: -.5rem;
  }

  .email-link {
    font-size: var(--text-base);
    padding: var(--space-sm) var(--space-md);
    text-align: center;
    display: block;
  }
}


/*# sourceMappingURL=src_48126d06._.css.map*/