{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* CSS Custom Properties */\n:root {\n  /* Colors - Greyscale with copper accent */\n  --color-black: #0a0a0a;\n  --color-dark-grey: #0a1322;\n  --color-medium-grey: #4a4a4a;\n  --color-light-grey: #8a8a8a;\n  --color-very-light-grey: #e5e5e5;\n  --color-white: #ffffff;\n  --color-copper: #3350b8;\n  --color-copper-light: #749ed4;\n  --color-copper-dark: #2b458b;\n\n  /* Typography */\n  --font-serif: 'Playfair Display', Georgia, serif;\n  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  --font-hero: 'Old Standard TT', Georgia, serif;\n\n  /* Font Sizes */\n  --text-xs: 0.75rem;    /* 12px */\n  --text-sm: 0.875rem;   /* 14px */\n  --text-base: 1rem;     /* 16px */\n  --text-lg: 1.125rem;   /* 18px */\n  --text-xl: 1.25rem;    /* 20px */\n  --text-2xl: 1.5rem;    /* 24px */\n  --text-3xl: 1.875rem;  /* 30px */\n  --text-4xl: 2.25rem;   /* 36px */\n  --text-5xl: 3rem;      /* 48px */\n  --text-6xl: 3.75rem;   /* 60px */\n  --text-8xl: 8.75rem;   /* 60px */\n\n  /* Spacing */\n  --space-xs: 0.5rem;    /* 8px */\n  --space-sm: 0.75rem;   /* 12px */\n  --space-md: 1rem;      /* 16px */\n  --space-lg: 1.5rem;    /* 24px */\n  --space-xl: 2rem;      /* 32px */\n  --space-2xl: 3rem;     /* 48px */\n  --space-3xl: 4rem;     /* 64px */\n  --space-4xl: 6rem;     /* 96px */\n  --space-5xl: 8rem;     /* 128px */\n\n  /* Layout */\n  --container-max-width: 1200px;\n  --container-padding: var(--space-lg);\n\n  /* Transitions */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s ease;\n  --transition-slow: 0.5s ease;\n}\n\n/* Reset and Base Styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n}\n\nbody {\n  font-family: var(--font-sans);\n  font-weight: 400;\n  line-height: 1.6;\n  color: var(--color-dark-grey);\n  background-color: var(--color-white);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Typography Styles */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-serif);\n  font-weight: 600;\n  line-height: 1.2;\n  margin-bottom: var(--space-md);\n  color: var(--color-black);\n}\n\nh1 {\n  font-size: var(--text-5xl);\n  font-weight: 700;\n}\n\nh2 {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n}\n\nh3 {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n}\n\nh4 {\n  font-size: var(--text-xl);\n  font-weight: 500;\n}\n\np {\n  margin-bottom: var(--space-lg);\n  font-size: var(--text-lg);\n  line-height: 1.7;\n}\n\na {\n  color: var(--color-copper);\n  text-decoration: none;\n  transition: color var(--transition-fast);\n}\n\na:hover {\n  color: var(--color-copper-dark);\n}\n\n/* Layout Components */\n.container {\n  max-width: var(--container-max-width);\n  margin: 0 auto;\n  padding: 0 var(--container-padding);\n}\n\n.section-title {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n  margin-bottom: var(--space-2xl);\n  text-align: center;\n  color: var(--color-black);\n}\n\n/* Utility Classes */\n.fade-in {\n  opacity: 0;\n  transform: translateY(20px);\n  transition: opacity var(--transition-slow), transform var(--transition-slow);\n}\n\n.fade-in.visible {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* Loading Animation */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Hero Section Styles */\n.hero-section {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.hero-image {\n  object-fit: cover;\n  object-position: top;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #000000b2 0%, #15296080 50%, #000c 100%);\n  z-index: 2;\n}\n\n.hero-content {\n  position: relative;\n  z-index: 3;\n  text-align: center;\n  color: var(--color-white);\n  max-width: 800px;\n  padding: 0 var(--space-lg);\n}\n\n.hero-name {\n  font-family: var(--font-hero);\n  font-size: var(--text-8xl);\n  font-weight: 100;\n  margin-bottom: var(--space-md);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  color: var(--color-white);\n  font-style: italic;\n  letter-spacing: -9%;\n}\n\n.hero-title {\n  font-size: var(--text-2xl);\n  font-weight: 400;\n  font-family: var(--font-sans);\n  margin-bottom: var(--space-lg);\n  color: var(--color-copper-light);\n}\n\n.hero-tagline {\n  font-size: var(--text-xl);\n  font-weight: 300;\n  font-style: italic;\n  opacity: 0.9;\n  margin-bottom: 0;\n}\n\n/* Responsive Typography */\n@media (max-width: 1200px) {\n  :root {\n    --container-padding: var(--space-lg);\n  }\n}\n\n@media (max-width: 768px) {\n  :root {\n    --text-5xl: 2.5rem;   /* 40px */\n    --text-4xl: 2rem;     /* 32px */\n    --text-3xl: 1.5rem;   /* 24px */\n    --text-6xl: 3rem;     /* 48px */\n    --container-padding: var(--space-md);\n    --space-5xl: 4rem;    /* Reduce section padding on mobile */\n    --space-4xl: 3rem;\n    --space-3xl: 2rem;\n  }\n\n  h1 {\n    font-size: var(--text-4xl);\n  }\n\n  h2 {\n    font-size: var(--text-3xl);\n  }\n\n  p {\n    font-size: var(--text-base);\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n  }\n\n  .hero-title {\n    font-size: var(--text-xl);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-lg);\n  }\n\n  .hero-section {\n    min-height: 500px;\n  }\n}\n\n@media (max-width: 480px) {\n  :root {\n    --text-6xl: 2.5rem;   /* 40px */\n    --text-5xl: 2rem;     /* 32px */\n    --text-4xl: 1.75rem;  /* 28px */\n    --text-3xl: 1.25rem;  /* 20px */\n    --container-padding: var(--space-sm);\n    --space-5xl: 3rem;\n    --space-4xl: 2rem;\n    --space-3xl: 1.5rem;\n  }\n\n  .hero-name {\n    font-size: var(--text-5xl);\n    line-height: 1.1;\n  }\n\n  .hero-title {\n    font-size: var(--text-lg);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-base);\n  }\n\n  .section-title {\n    font-size: var(--text-3xl);\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;;;;;AASA;;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;;AASA;EACE;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;;EAWA;;;;;EAKA;;;;EAIA;;;;EAIA"}}]}