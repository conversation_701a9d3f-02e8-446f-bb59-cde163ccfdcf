{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* CSS Custom Properties */\n:root {\n  /* Enhanced Color Palette */\n  --color-black: #0a0a0a;\n  --color-dark-grey: #2c3e50;\n  --color-medium-grey: #4a4a4a;\n  --color-light-grey: #8a8a8a;\n  --color-very-light-grey: #e5e5e5;\n  --color-white: #ffffff;\n\n  /* Primary accent colors */\n  --color-copper: #3350b8;\n  --color-copper-light: #749ed4;\n  --color-copper-dark: #2b458b;\n\n  /* Additional accent colors for sections */\n  --color-teal: #16a085;\n  --color-teal-light: #48c9b0;\n  --color-teal-dark: #138d75;\n\n  --color-purple: #8e44ad;\n  --color-purple-light: #bb8fce;\n  --color-purple-dark: #7d3c98;\n\n  --color-orange: #e67e22;\n  --color-orange-light: #f39c12;\n  --color-orange-dark: #d35400;\n\n  --color-emerald: #27ae60;\n  --color-emerald-light: #58d68d;\n  --color-emerald-dark: #229954;\n\n  /* Typography */\n  --font-serif: 'Playfair Display', Georgia, serif;\n  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  --font-hero: 'Old Standard TT', Georgia, serif;\n\n  /* Font Sizes */\n  --text-xs: 0.75rem;    /* 12px */\n  --text-sm: 0.875rem;   /* 14px */\n  --text-base: 1rem;     /* 16px */\n  --text-lg: 1.125rem;   /* 18px */\n  --text-xl: 1.25rem;    /* 20px */\n  --text-2xl: 1.5rem;    /* 24px */\n  --text-3xl: 1.875rem;  /* 30px */\n  --text-4xl: 2.25rem;   /* 36px */\n  --text-5xl: 3rem;      /* 48px */\n  --text-6xl: 3.75rem;   /* 60px */\n  --text-8xl: 8.75rem;   /* 60px */\n\n  /* Mobile-First Spacing System */\n  --space-xs: 0.5rem;    /* 8px */\n  --space-sm: 0.75rem;   /* 12px */\n  --space-md: 1rem;      /* 16px */\n  --space-lg: 1.5rem;    /* 24px */\n  --space-xl: 2rem;      /* 32px */\n  --space-2xl: 2.5rem;   /* 40px - reduced for mobile */\n  --space-3xl: 3rem;     /* 48px - reduced for mobile */\n  --space-4xl: 4rem;     /* 64px - reduced for mobile */\n  --space-5xl: 5rem;     /* 80px - reduced for mobile */\n\n  /* Touch-friendly sizes */\n  --touch-target: 44px;  /* Minimum touch target size */\n  --button-height: 48px; /* Standard button height */\n\n  /* Mobile-First Layout System */\n  --container-max-width: 1200px;\n  --container-padding: var(--space-md);  /* Start with mobile padding */\n  --container-padding-lg: var(--space-lg);\n  --container-padding-xl: var(--space-xl);\n\n  /* Responsive breakpoints */\n  --breakpoint-sm: 640px;\n  --breakpoint-md: 768px;\n  --breakpoint-lg: 1024px;\n  --breakpoint-xl: 1280px;\n\n  /* Transitions & Animations */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s ease;\n  --transition-slow: 0.5s ease;\n  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);\n  --transition-smooth: 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n\n  /* Shadows & Depth */\n  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);\n  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);\n  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);\n  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);\n  --shadow-glow: 0 0 20px rgba(51, 80, 184, 0.3);\n\n  /* Blur & Effects */\n  --blur-sm: blur(4px);\n  --blur-md: blur(8px);\n  --blur-lg: blur(16px);\n\n  /* Animation Durations */\n  --duration-fast: 200ms;\n  --duration-normal: 400ms;\n  --duration-slow: 600ms;\n  --duration-slower: 800ms;\n}\n\n/* Reset and Base Styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n  overflow-x: hidden;\n}\n\nbody {\n  font-family: var(--font-sans);\n  font-weight: 400;\n  line-height: 1.6;\n  color: var(--color-dark-grey);\n  background-color: var(--color-white);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow-x: hidden;\n  position: relative;\n}\n\n/* Enhanced scrollbar styling */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--color-very-light-grey);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--color-copper);\n  border-radius: 4px;\n  transition: background var(--transition-fast);\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--color-copper-dark);\n}\n\n/* Performance optimizations */\n* {\n  will-change: auto;\n}\n\n/* Mobile-specific optimizations */\n@media (max-width: 767px) {\n  /* Improve mobile scrolling performance */\n  body {\n    -webkit-overflow-scrolling: touch;\n  }\n\n  /* Optimize mobile interactions */\n  button, a, .interest-item, .achievement-item, .logo-item {\n    -webkit-tap-highlight-color: rgba(51, 80, 184, 0.2);\n  }\n\n  /* Prevent zoom on input focus (if we add forms later) */\n  input, textarea, select {\n    font-size: 16px;\n  }\n}\n\n.hero-image,\n.timeline-item,\n.interest-item,\n.achievement-item,\n.logo-item {\n  will-change: transform;\n}\n\n/* Reduce motion for users who prefer it */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n\n  .hero-image {\n    transform: none !important;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  :root {\n    --color-copper: #0066cc;\n    --color-copper-light: #3399ff;\n    --color-copper-dark: #003d7a;\n  }\n\n  .hero-overlay {\n    background: rgba(0, 0, 0, 0.8);\n  }\n}\n\n/* Remove problematic dark mode that's causing color issues */\n\n/* Typography Styles */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-serif);\n  font-weight: 600;\n  line-height: 1.2;\n  margin-bottom: var(--space-md);\n  color: var(--color-black) !important;  /* Force dark text for headings */\n}\n\nh1 {\n  font-size: var(--text-5xl);\n  font-weight: 700;\n}\n\nh2 {\n  font-size: var(--text-4xl);\n  font-weight: 600;\n}\n\nh3 {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n}\n\nh4 {\n  font-size: var(--text-xl);\n  font-weight: 500;\n}\n\np {\n  margin-bottom: var(--space-lg);\n  font-size: var(--text-lg);\n  line-height: 1.7;\n  color: var(--color-dark-grey);  /* Ensure readable text color */\n}\n\na {\n  color: var(--color-copper);\n  text-decoration: none;\n  transition: color var(--transition-fast);\n}\n\na:hover {\n  color: var(--color-copper-dark);\n}\n\n/* Enhanced Mobile-First Layout Components */\n.container {\n  max-width: var(--container-max-width);\n  margin: 0 auto;\n  padding: 0 var(--container-padding);\n  width: 100%;\n}\n\n/* Responsive container padding */\n@media (min-width: 640px) {\n  .container {\n    padding: 0 var(--container-padding-lg);\n  }\n}\n\n@media (min-width: 1024px) {\n  .container {\n    padding: 0 var(--container-padding-xl);\n  }\n}\n\n/* Content width utilities */\n.content-narrow {\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.content-medium {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.content-wide {\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.section-title {\n  font-size: var(--text-3xl);  /* Start smaller for mobile */\n  font-weight: 600;\n  margin-bottom: var(--space-xl);  /* Reduced spacing for mobile */\n  text-align: center;\n  color: var(--color-black);\n  line-height: 1.2;\n  padding: 0 var(--space-md);  /* Add padding for mobile */\n}\n\n/* Responsive section title sizing */\n@media (min-width: 640px) {\n  .section-title {\n    font-size: var(--text-4xl);\n    margin-bottom: var(--space-2xl);\n    padding: 0;\n  }\n}\n\n/* Enhanced Utility Classes */\n.fade-in {\n  opacity: 0;\n  transform: translateY(20px);\n  transition: opacity var(--transition-slow), transform var(--transition-slow);\n}\n\n.fade-in.visible {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* Advanced Animation Classes */\n.animate-fade-in-up {\n  animation: fadeInUp var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-down {\n  animation: fadeInDown var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-left {\n  animation: fadeInLeft var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-fade-in-right {\n  animation: fadeInRight var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-scale-in {\n  animation: scaleIn var(--duration-normal) var(--transition-bounce) forwards;\n}\n\n.animate-slide-in-bottom {\n  animation: slideInFromBottom var(--duration-slow) var(--transition-smooth) forwards;\n}\n\n.animate-float {\n  animation: float 3s ease-in-out infinite;\n}\n\n.animate-pulse {\n  animation: pulse 2s ease-in-out infinite;\n}\n\n.animate-glow {\n  animation: glow 2s ease-in-out infinite;\n}\n\n/* Hover Enhancement Classes */\n.hover-lift {\n  transition: transform var(--transition-normal), box-shadow var(--transition-normal);\n}\n\n.hover-lift:hover {\n  transform: translateY(-8px);\n  box-shadow: var(--shadow-xl);\n}\n\n.hover-scale {\n  transition: transform var(--transition-bounce);\n}\n\n.hover-scale:hover {\n  transform: scale(1.05);\n}\n\n.hover-glow {\n  transition: box-shadow var(--transition-normal);\n}\n\n.hover-glow:hover {\n  box-shadow: var(--shadow-glow);\n}\n\n/* Loading States */\n.loading-shimmer {\n  background: linear-gradient(\n    90deg,\n    var(--color-very-light-grey) 0%,\n    var(--color-white) 50%,\n    var(--color-very-light-grey) 100%\n  );\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n/* Intersection Observer Enhanced Classes */\n.reveal {\n  opacity: 0;\n  transform: translateY(50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal.revealed {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.reveal-scale {\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all var(--duration-normal) var(--transition-bounce);\n}\n\n.reveal-scale.revealed {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.reveal-slide-left {\n  opacity: 0;\n  transform: translateX(-50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal-slide-left.revealed {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n.reveal-slide-right {\n  opacity: 0;\n  transform: translateX(50px);\n  transition: all var(--duration-slow) var(--transition-smooth);\n}\n\n.reveal-slide-right.revealed {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* Advanced Keyframe Animations */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInDown {\n  0% {\n    opacity: 0;\n    transform: translateY(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInLeft {\n  0% {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes fadeInRight {\n  0% {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes scaleIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes slideInFromBottom {\n  0% {\n    opacity: 0;\n    transform: translateY(50px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n@keyframes glow {\n  0%, 100% {\n    box-shadow: 0 0 5px var(--color-copper);\n  }\n  50% {\n    box-shadow: 0 0 20px var(--color-copper), 0 0 30px var(--color-copper-light);\n  }\n}\n\n/* Enhanced Hero Section Styles */\n.hero-section {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: var(--color-black);\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  will-change: transform;\n}\n\n.hero-image {\n  object-fit: cover;\n  object-position: top;\n  transition: transform var(--duration-slower) ease-out;\n  will-change: transform;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.7) 0%,\n    rgba(21, 41, 96, 0.5) 50%,\n    rgba(0, 0, 0, 0.8) 100%\n  );\n  z-index: 2;\n}\n\n/* Add subtle animated overlay patterns */\n.hero-overlay::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(\n    circle at 20% 80%,\n    rgba(51, 80, 184, 0.1) 0%,\n    transparent 50%\n  ),\n  radial-gradient(\n    circle at 80% 20%,\n    rgba(51, 80, 184, 0.1) 0%,\n    transparent 50%\n  );\n  animation: float 6s ease-in-out infinite;\n}\n\n.hero-content {\n  position: relative;\n  z-index: 3;\n  text-align: center;\n  color: var(--color-white);\n  max-width: 900px;\n  padding: 0 var(--space-lg);\n  animation: heroContentReveal 0.8s var(--transition-smooth) forwards;\n}\n\n@keyframes heroContentReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(40px) scale(0.95);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.hero-name {\n  font-family: var(--font-hero);\n  font-size: var(--text-8xl);\n  font-weight: 100;\n  margin-bottom: var(--space-md);\n  text-shadow:\n    2px 2px 4px rgba(0, 0, 0, 0.7),\n    0 0 20px rgba(255, 255, 255, 0.1);\n  color: var(--color-white);\n  font-style: italic;\n  letter-spacing: -0.02em;\n  opacity: 0;\n  animation: heroNameReveal 0.6s var(--transition-smooth) 0.1s forwards;\n  position: relative;\n}\n\n@keyframes heroNameReveal {\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n    filter: blur(10px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n    filter: blur(0);\n  }\n}\n\n.hero-name::after {\n  content: '';\n  position: absolute;\n  bottom: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 0;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, var(--color-white), transparent);\n  animation: underlineExpand 0.8s ease-out 0.7s forwards;\n}\n\n@keyframes underlineExpand {\n  0% {\n    width: 0;\n  }\n  100% {\n    width: 60%;\n  }\n}\n\n.hero-title {\n  font-size: var(--text-2xl);\n  font-weight: 400;\n  font-family: var(--font-sans);\n  margin-bottom: var(--space-lg);\n  color: var(--color-very-light-grey);\n  opacity: 0;\n  animation: heroTitleReveal 0.6s var(--transition-smooth) 0.3s forwards;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n@keyframes heroTitleReveal {\n  0% {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.hero-tagline {\n  font-size: var(--text-xl);\n  font-weight: 300;\n  font-style: italic;\n  opacity: 0;\n  margin-bottom: 0;\n  color: var(--color-light-grey);\n  animation: heroTaglineReveal 0.6s var(--transition-smooth) 0.5s forwards;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n@keyframes heroTaglineReveal {\n  0% {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  100% {\n    opacity: 0.9;\n    transform: translateX(0);\n  }\n}\n\n/* Mobile-First Responsive Typography */\n/* Small tablets and large phones */\n@media (min-width: 640px) {\n  :root {\n    --text-5xl: 3rem;     /* 48px */\n    --text-4xl: 2.25rem;  /* 36px */\n    --text-3xl: 1.875rem; /* 30px */\n    --space-2xl: 3rem;    /* 48px */\n    --space-3xl: 4rem;    /* 64px */\n    --space-4xl: 5rem;    /* 80px */\n    --space-5xl: 6rem;    /* 96px */\n  }\n\n  .hero-name {\n    font-size: var(--text-6xl);\n  }\n\n  .hero-title {\n    font-size: var(--text-2xl);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-xl);\n  }\n\n  .hero-section {\n    min-height: 600px;\n  }\n}\n\n/* Tablets */\n@media (min-width: 768px) {\n  :root {\n    --text-6xl: 3.75rem;  /* 60px */\n    --text-5xl: 3rem;     /* 48px */\n    --text-4xl: 2.25rem;  /* 36px */\n    --space-3xl: 4rem;    /* 64px */\n    --space-4xl: 6rem;    /* 96px */\n    --space-5xl: 8rem;    /* 128px */\n  }\n\n  h1 {\n    font-size: var(--text-5xl);\n  }\n\n  h2 {\n    font-size: var(--text-4xl);\n  }\n\n  p {\n    font-size: var(--text-lg);\n  }\n\n  .hero-section {\n    min-height: 700px;\n  }\n}\n\n/* Desktop */\n@media (min-width: 1024px) {\n  :root {\n    --text-8xl: 6rem;     /* 96px - reduced from 140px for better mobile scaling */\n    --text-6xl: 3.75rem;  /* 60px */\n    --text-5xl: 3rem;     /* 48px */\n    --space-4xl: 6rem;    /* 96px */\n    --space-5xl: 8rem;    /* 128px */\n  }\n\n  .hero-name {\n    font-size: var(--text-8xl);\n  }\n\n  .hero-section {\n    min-height: 100vh;\n  }\n}\n\n/* Large desktop */\n@media (min-width: 1280px) {\n  :root {\n    --text-8xl: 8.75rem;  /* Original size for large screens */\n  }\n}\n\n/* Mobile-specific adjustments */\n@media (max-width: 639px) {\n  .hero-name {\n    font-size: var(--text-5xl);\n    line-height: 1.1;\n    letter-spacing: -0.01em;\n  }\n\n  .hero-title {\n    font-size: var(--text-xl);\n  }\n\n  .hero-tagline {\n    font-size: var(--text-lg);\n  }\n\n  .hero-section {\n    min-height: 500px;\n    padding: var(--space-lg) 0;\n  }\n\n  .hero-content {\n    padding: 0 var(--space-lg);\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;AAKA;EAEE;;;;EAKA;;;;EAKA;;;;;AAKF;;;;AASA;EACE;;;;;;;EASA;;;;;AAMF;EACE;;;;;;EAMA;;;;;AAQF;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;AAKA;;;;;;;AAQA;EACE;;;;;AAKF;EACE;;;;;AAMF;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAWA;EACE;;;;;;;AAQF;;;;;;AAMA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAYA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;;;AAoBA;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;AAaA;;;;;;;;;;;;AAYA;;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAaA;EACE;;;;;;;;;;EAUA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;EACE;;;;;;;;;EASA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;EACE;;;;;;;;EAQA;;;;EAIA;;;;;AAMF;EACE;;;;;AAMF;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}