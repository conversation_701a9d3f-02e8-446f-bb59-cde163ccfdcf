/* [project]/src/app/globals.css [app-client] (css) */
:root {
  --color-black: #0a0a0a;
  --color-dark-grey: #2c3e50;
  --color-medium-grey: #4a4a4a;
  --color-light-grey: #8a8a8a;
  --color-very-light-grey: #e5e5e5;
  --color-white: #fff;
  --color-copper: #3350b8;
  --color-copper-light: #749ed4;
  --color-copper-dark: #2b458b;
  --color-teal: #16a085;
  --color-teal-light: #48c9b0;
  --color-teal-dark: #138d75;
  --color-purple: #8e44ad;
  --color-purple-light: #bb8fce;
  --color-purple-dark: #7d3c98;
  --color-orange: #e67e22;
  --color-orange-light: #f39c12;
  --color-orange-dark: #d35400;
  --color-emerald: #27ae60;
  --color-emerald-light: #58d68d;
  --color-emerald-dark: #229954;
  --font-serif: "Playfair Display", Georgia, serif;
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-hero: "Old Standard TT", Georgia, serif;
  --text-xs: .75rem;
  --text-sm: .875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-8xl: 8.75rem;
  --space-xs: .5rem;
  --space-sm: .75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 2.5rem;
  --space-3xl: 3rem;
  --space-4xl: 4rem;
  --space-5xl: 5rem;
  --touch-target: 44px;
  --button-height: 48px;
  --container-max-width: 1200px;
  --container-padding: var(--space-md);
  --container-padding-lg: var(--space-lg);
  --container-padding-xl: var(--space-xl);
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --transition-fast: .15s ease;
  --transition-normal: .3s ease;
  --transition-slow: .5s ease;
  --transition-bounce: .4s cubic-bezier(.68, -.55, .265, 1.55);
  --transition-smooth: .6s cubic-bezier(.4, 0, .2, 1);
  --transition-elastic: .8s cubic-bezier(.175, .885, .32, 1.275);
  --shadow-sm: 0 1px 2px #0000000d;
  --shadow-md: 0 4px 6px #00000012;
  --shadow-lg: 0 10px 15px #0000001a;
  --shadow-xl: 0 20px 25px #00000026;
  --shadow-2xl: 0 25px 50px #00000040;
  --shadow-glow: 0 0 20px #3350b84d;
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);
  --duration-fast: .2s;
  --duration-normal: .4s;
  --duration-slow: .6s;
  --duration-slower: .8s;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  overflow-x: hidden;
}

body {
  font-family: var(--font-sans);
  color: var(--color-dark-grey);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.6;
  position: relative;
  overflow-x: hidden;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-very-light-grey);
}

::-webkit-scrollbar-thumb {
  background: var(--color-copper);
  transition: background var(--transition-fast);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-copper-dark);
}

* {
  will-change: auto;
}

@media (width <= 767px) {
  body {
    -webkit-overflow-scrolling: touch;
  }

  button, a, .interest-item, .achievement-item, .logo-item {
    -webkit-tap-highlight-color: #3350b833;
  }

  input, textarea, select {
    font-size: 16px;
  }
}

.hero-image, .timeline-item, .interest-item, .achievement-item, .logo-item {
  will-change: transform;
}

@media (prefers-reduced-motion: reduce) {
  *, :before, :after {
    scroll-behavior: auto !important;
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }

  .hero-image {
    transform: none !important;
  }
}

@media (prefers-contrast: high) {
  :root {
    --color-copper: #06c;
    --color-copper-light: #39f;
    --color-copper-dark: #003d7a;
  }

  .hero-overlay {
    background: #000c;
  }
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  margin-bottom: var(--space-md);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-black) !important;
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 600;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-lg);
  font-size: var(--text-lg);
  color: var(--color-dark-grey);
  line-height: 1.7;
}

a {
  color: var(--color-copper);
  transition: color var(--transition-fast);
  text-decoration: none;
}

a:hover {
  color: var(--color-copper-dark);
}

.container {
  max-width: var(--container-max-width);
  padding: 0 var(--container-padding);
  width: 100%;
  margin: 0 auto;
}

@media (width >= 640px) {
  .container {
    padding: 0 var(--container-padding-lg);
  }
}

@media (width >= 1024px) {
  .container {
    padding: 0 var(--container-padding-xl);
  }
}

.content-narrow {
  max-width: 600px;
  margin: 0 auto;
}

.content-medium {
  max-width: 800px;
  margin: 0 auto;
}

.content-wide {
  max-width: 1000px;
  margin: 0 auto;
}

.section-title {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-xl);
  text-align: center;
  color: var(--color-black);
  padding: 0 var(--space-md);
  font-weight: 600;
  line-height: 1.2;
}

@media (width >= 640px) {
  .section-title {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-2xl);
    padding: 0;
  }
}

.fade-in {
  opacity: 0;
  transition: opacity var(--transition-slow), transform var(--transition-slow);
  transform: translateY(20px);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-up {
  animation: fadeInUp var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--transition-bounce) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom var(--duration-slow) var(--transition-smooth) forwards;
}

.animate-float {
  animation: 3s ease-in-out infinite float;
}

.animate-pulse {
  animation: 2s ease-in-out infinite pulse;
}

.animate-glow {
  animation: 2s ease-in-out infinite glow;
}

.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-8px);
}

.hover-scale {
  transition: transform var(--transition-bounce);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--color-very-light-grey) 0%, var(--color-white) 50%, var(--color-very-light-grey) 100%);
  background-size: 200% 100%;
  animation: 1.5s infinite shimmer;
}

.reveal {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateY(50px);
}

.reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-scale {
  opacity: 0;
  transition: all var(--duration-normal) var(--transition-bounce);
  transform: scale(.8);
}

.reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

.reveal-slide-left {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateX(-50px);
}

.reveal-slide-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-slide-right {
  opacity: 0;
  transition: all var(--duration-slow) var(--transition-smooth);
  transform: translateX(50px);
}

.reveal-slide-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-copper);
  }

  50% {
    box-shadow: 0 0 20px var(--color-copper), 0 0 30px var(--color-copper-light);
  }
}

.hero-section {
  background: var(--color-black);
  justify-content: center;
  align-items: center;
  height: 100vh;
  min-height: 600px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.hero-background {
  z-index: 1;
  will-change: transform;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-image {
  object-fit: cover;
  object-position: top;
  transition: transform var(--duration-slower) ease-out;
  will-change: transform;
}

.hero-overlay {
  z-index: 2;
  background: linear-gradient(135deg, #000000b3 0%, #15296080 50%, #000c 100%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-overlay:before {
  content: "";
  background: radial-gradient(circle at 20% 80%, #3350b81a 0%, #0000 50%), radial-gradient(circle at 80% 20%, #3350b81a 0%, #0000 50%);
  width: 100%;
  height: 100%;
  animation: 6s ease-in-out infinite float;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-content {
  z-index: 3;
  text-align: center;
  color: var(--color-white);
  max-width: 900px;
  padding: 0 var(--space-lg);
  animation: heroContentReveal .8s var(--transition-smooth) forwards;
  position: relative;
}

@keyframes heroContentReveal {
  0% {
    opacity: 0;
    transform: translateY(40px)scale(.95);
  }

  100% {
    opacity: 1;
    transform: translateY(0)scale(1);
  }
}

.hero-name {
  font-family: var(--font-hero);
  font-size: var(--text-8xl);
  margin-bottom: var(--space-md);
  text-shadow: 2px 2px 4px #000000b3, 0 0 20px #ffffff1a;
  color: var(--color-white);
  letter-spacing: -.02em;
  opacity: 0;
  animation: heroNameReveal .6s var(--transition-smooth) .1s forwards;
  font-style: italic;
  font-weight: 100;
  position: relative;
}

@keyframes heroNameReveal {
  0% {
    opacity: 0;
    filter: blur(10px);
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    filter: blur();
    transform: translateY(0);
  }
}

.hero-name:after {
  content: "";
  background: linear-gradient(90deg, transparent, var(--color-white), transparent);
  width: 0;
  height: 2px;
  animation: .8s ease-out .7s forwards underlineExpand;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

@keyframes underlineExpand {
  0% {
    width: 0;
  }

  100% {
    width: 60%;
  }
}

.hero-title {
  font-size: var(--text-2xl);
  font-weight: 400;
  font-family: var(--font-sans);
  margin-bottom: var(--space-lg);
  color: var(--color-very-light-grey);
  opacity: 0;
  animation: heroTitleReveal .6s var(--transition-smooth) .3s forwards;
  text-shadow: 1px 1px 2px #00000080;
}

@keyframes heroTitleReveal {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-tagline {
  font-size: var(--text-xl);
  opacity: 0;
  color: var(--color-light-grey);
  animation: heroTaglineReveal .6s var(--transition-smooth) .5s forwards;
  text-shadow: 1px 1px 2px #00000080;
  margin-bottom: 0;
  font-style: italic;
  font-weight: 300;
}

@keyframes heroTaglineReveal {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: .9;
    transform: translateX(0);
  }
}

@media (width >= 640px) {
  :root {
    --text-5xl: 3rem;
    --text-4xl: 2.25rem;
    --text-3xl: 1.875rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 5rem;
    --space-5xl: 6rem;
  }

  .hero-name {
    font-size: var(--text-6xl);
  }

  .hero-title {
    font-size: var(--text-2xl);
  }

  .hero-tagline {
    font-size: var(--text-xl);
  }

  .hero-section {
    min-height: 600px;
  }
}

@media (width >= 768px) {
  :root {
    --text-6xl: 3.75rem;
    --text-5xl: 3rem;
    --text-4xl: 2.25rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;
    --space-5xl: 8rem;
  }

  h1 {
    font-size: var(--text-5xl);
  }

  h2 {
    font-size: var(--text-4xl);
  }

  p {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 700px;
  }
}

@media (width >= 1024px) {
  :root {
    --text-8xl: 6rem;
    --text-6xl: 3.75rem;
    --text-5xl: 3rem;
    --space-4xl: 6rem;
    --space-5xl: 8rem;
  }

  .hero-name {
    font-size: var(--text-8xl);
  }

  .hero-section {
    min-height: 100vh;
  }
}

@media (width >= 1280px) {
  :root {
    --text-8xl: 8.75rem;
  }
}

@media (width <= 639px) {
  .hero-name {
    font-size: var(--text-5xl);
    letter-spacing: -.01em;
    line-height: 1.1;
  }

  .hero-title {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 500px;
    padding: var(--space-lg) 0;
  }

  .hero-content {
    padding: 0 var(--space-lg);
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/