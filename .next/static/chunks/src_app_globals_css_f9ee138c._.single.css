/* [project]/src/app/globals.css [app-client] (css) */
:root {
  --color-black: #0a0a0a;
  --color-dark-grey: #0a1322;
  --color-medium-grey: #4a4a4a;
  --color-light-grey: #8a8a8a;
  --color-very-light-grey: #e5e5e5;
  --color-white: #fff;
  --color-copper: #3350b8;
  --color-copper-light: #749ed4;
  --color-copper-dark: #2b458b;
  --font-serif: "Playfair Display", Georgia, serif;
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-hero: "Old Standard TT", Georgia, serif;
  --text-xs: .75rem;
  --text-sm: .875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-8xl: 8.75rem;
  --space-xs: .5rem;
  --space-sm: .75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
  --container-max-width: 1200px;
  --container-padding: var(--space-lg);
  --transition-fast: .15s ease;
  --transition-normal: .3s ease;
  --transition-slow: .5s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-sans);
  color: var(--color-dark-grey);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  margin-bottom: var(--space-md);
  color: var(--color-black);
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 600;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-lg);
  font-size: var(--text-lg);
  line-height: 1.7;
}

a {
  color: var(--color-copper);
  transition: color var(--transition-fast);
  text-decoration: none;
}

a:hover {
  color: var(--color-copper-dark);
}

.container {
  max-width: var(--container-max-width);
  padding: 0 var(--container-padding);
  margin: 0 auto;
}

.section-title {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-2xl);
  text-align: center;
  color: var(--color-black);
  font-weight: 600;
}

.fade-in {
  opacity: 0;
  transition: opacity var(--transition-slow), transform var(--transition-slow);
  transform: translateY(20px);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.hero-section {
  justify-content: center;
  align-items: center;
  height: 100vh;
  min-height: 600px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.hero-background {
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-image {
  object-fit: cover;
  object-position: top;
}

.hero-overlay {
  z-index: 2;
  background: linear-gradient(135deg, #000000b2 0%, #15296080 50%, #000c 100%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-content {
  z-index: 3;
  text-align: center;
  color: var(--color-white);
  max-width: 800px;
  padding: 0 var(--space-lg);
  position: relative;
}

.hero-name {
  font-family: var(--font-hero);
  font-size: var(--text-8xl);
  margin-bottom: var(--space-md);
  text-shadow: 2px 2px 4px #00000080;
  color: var(--color-white);
  letter-spacing: -9%;
  font-style: italic;
  font-weight: 100;
}

.hero-title {
  font-size: var(--text-2xl);
  font-weight: 400;
  font-family: var(--font-sans);
  margin-bottom: var(--space-lg);
  color: var(--color-copper-light);
}

.hero-tagline {
  font-size: var(--text-xl);
  opacity: .9;
  margin-bottom: 0;
  font-style: italic;
  font-weight: 300;
}

@media (width <= 1200px) {
  :root {
    --container-padding: var(--space-lg);
  }
}

@media (width <= 768px) {
  :root {
    --text-5xl: 2.5rem;
    --text-4xl: 2rem;
    --text-3xl: 1.5rem;
    --text-6xl: 3rem;
    --container-padding: var(--space-md);
    --space-5xl: 4rem;
    --space-4xl: 3rem;
    --space-3xl: 2rem;
  }

  h1 {
    font-size: var(--text-4xl);
  }

  h2 {
    font-size: var(--text-3xl);
  }

  p {
    font-size: var(--text-base);
  }

  .hero-name {
    font-size: var(--text-5xl);
  }

  .hero-title {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-lg);
  }

  .hero-section {
    min-height: 500px;
  }
}

@media (width <= 480px) {
  :root {
    --text-6xl: 2.5rem;
    --text-5xl: 2rem;
    --text-4xl: 1.75rem;
    --text-3xl: 1.25rem;
    --container-padding: var(--space-sm);
    --space-5xl: 3rem;
    --space-4xl: 2rem;
    --space-3xl: 1.5rem;
  }

  .hero-name {
    font-size: var(--text-5xl);
    line-height: 1.1;
  }

  .hero-title {
    font-size: var(--text-lg);
  }

  .hero-tagline {
    font-size: var(--text-base);
  }

  .section-title {
    font-size: var(--text-3xl);
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/