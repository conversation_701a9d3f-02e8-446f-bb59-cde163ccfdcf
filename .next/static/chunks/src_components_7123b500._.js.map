{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function ScrollAnimations() {\n  // Performance-optimized observer callback\n  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {\n    entries.forEach((entry) => {\n      const element = entry.target as HTMLElement;\n      const intersectionRatio = entry.intersectionRatio;\n\n      if (entry.isIntersecting) {\n        // Add visible class for basic animations\n        element.classList.add('visible', 'revealed');\n\n        // Add progressive reveal based on intersection ratio\n        if (intersectionRatio > 0.5) {\n          element.classList.add('fully-visible');\n        }\n\n        // Trigger staggered animations for child elements\n        const children = element.querySelectorAll('.stagger-child');\n        children.forEach((child, index) => {\n          setTimeout(() => {\n            (child as HTMLElement).classList.add('revealed');\n          }, index * 100);\n        });\n      } else {\n        // Optional: Remove classes for re-animation on scroll up\n        if (intersectionRatio === 0) {\n          element.classList.remove('fully-visible');\n        }\n      }\n    });\n  }, []);\n\n  useEffect(() => {\n    // Enhanced Intersection Observer for sophisticated animations\n    const observerOptions = {\n      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver(observerCallback, observerOptions);\n\n    // Enhanced section animations with different reveal types\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      const htmlSection = section as HTMLElement;\n\n      // Alternate animation types for visual variety\n      const animationType = index % 3;\n      switch (animationType) {\n        case 0:\n          htmlSection.classList.add('reveal');\n          break;\n        case 1:\n          htmlSection.classList.add('reveal-scale');\n          break;\n        case 2:\n          htmlSection.classList.add('reveal-slide-left');\n          break;\n      }\n\n      htmlSection.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Enhanced timeline items with staggered reveals\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-slide-right');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n\n      // Add stagger-child class to timeline content\n      const content = htmlItem.querySelector('.timeline-content');\n      if (content) {\n        content.classList.add('stagger-child');\n      }\n\n      observer.observe(item);\n    });\n\n    // Enhanced interest items with hover effects\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced achievement items with scale animations\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced logo items with glow effects\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-glow');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Enhanced parallax effect with multiple layers\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n          const heroOverlay = document.querySelector('.hero-overlay') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            // Reduced parallax for hero image (faster, more responsive)\n            const imageRate = scrolled * 0.15;  // Reduced from 0.3\n            heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.05)`;  // Reduced scale\n\n            // Minimal parallax for overlay\n            if (heroOverlay) {\n              const overlayRate = scrolled * 0.05;  // Reduced from 0.1\n              heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;\n            }\n\n            // Faster fade out hero content as user scrolls\n            const heroContent = document.querySelector('.hero-content') as HTMLElement;\n            if (heroContent) {\n              const fadeRate = Math.max(0, 1 - (scrolled / (window.innerHeight * 0.5)));  // Faster fade\n              heroContent.style.opacity = fadeRate.toString();\n              heroContent.style.transform = `translateY(${scrolled * 0.1}px)`;  // Reduced movement\n            }\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, [observerCallback]);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,QAAQ,OAAO;kEAAC,CAAC;oBACf,MAAM,UAAU,MAAM,MAAM;oBAC5B,MAAM,oBAAoB,MAAM,iBAAiB;oBAEjD,IAAI,MAAM,cAAc,EAAE;wBACxB,yCAAyC;wBACzC,QAAQ,SAAS,CAAC,GAAG,CAAC,WAAW;wBAEjC,qDAAqD;wBACrD,IAAI,oBAAoB,KAAK;4BAC3B,QAAQ,SAAS,CAAC,GAAG,CAAC;wBACxB;wBAEA,kDAAkD;wBAClD,MAAM,WAAW,QAAQ,gBAAgB,CAAC;wBAC1C,SAAS,OAAO;8EAAC,CAAC,OAAO;gCACvB;sFAAW;wCACR,MAAsB,SAAS,CAAC,GAAG,CAAC;oCACvC;qFAAG,QAAQ;4BACb;;oBACF,OAAO;wBACL,yDAAyD;wBACzD,IAAI,sBAAsB,GAAG;4BAC3B,QAAQ,SAAS,CAAC,MAAM,CAAC;wBAC3B;oBACF;gBACF;;QACF;yDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,8DAA8D;YAC9D,MAAM,kBAAkB;gBACtB,WAAW;oBAAC;oBAAG;oBAAK;oBAAM;oBAAK;oBAAM;iBAAE;gBACvC,YAAY;YACd;YAEA,MAAM,WAAW,IAAI,qBAAqB,kBAAkB;YAE5D,0DAA0D;YAC1D,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO;8CAAC,CAAC,SAAS;oBACzB,MAAM,cAAc;oBAEpB,+CAA+C;oBAC/C,MAAM,gBAAgB,QAAQ;oBAC9B,OAAQ;wBACN,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;wBACF,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;wBACF,KAAK;4BACH,YAAY,SAAS,CAAC,GAAG,CAAC;4BAC1B;oBACJ;oBAEA,YAAY,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBACrD,SAAS,OAAO,CAAC;gBACnB;;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC;oBACvB,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAElD,8CAA8C;oBAC9C,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,IAAI,SAAS;wBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;oBAEA,SAAS,OAAO,CAAC;gBACnB;;YAEA,6CAA6C;YAC7C,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO;8CAAC,CAAC,MAAM;oBAC3B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,UAAU;oBACjC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAClD,SAAS,OAAO,CAAC;gBACnB;;YAEA,mDAAmD;YACnD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO;8CAAC,CAAC,MAAM;oBAC9B,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;oBACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;oBACnD,SAAS,OAAO,CAAC;gBACnB;;YAEA,wCAAwC;YACxC,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,UAAU,OAAO;8CAAC,CAAC,MAAM;oBACvB,MAAM,WAAW;oBACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;oBACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;oBAClD,SAAS,OAAO,CAAC;gBACnB;;YAEA,kDAAkD;YAClD,MAAM;iEAAqB,CAAC;oBAC1B,MAAM,SAAS,EAAE,MAAM;oBACvB,IAAI,OAAO,IAAI,EAAE;wBACf,EAAE,cAAc;wBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;wBAClD,IAAI,SAAS;4BACX,QAAQ,cAAc,CAAC;gCACrB,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;;YAEA,wCAAwC;YACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;YAC9C,YAAY,OAAO;8CAAC,CAAA;oBAClB,KAAK,gBAAgB,CAAC,SAAS;gBACjC;;YAEA,gDAAgD;YAChD,IAAI,UAAU;YACd,MAAM;6DAAiB;oBACrB,IAAI,CAAC,SAAS;wBACZ;yEAAsB;gCACpB,MAAM,WAAW,OAAO,WAAW;gCACnC,MAAM,cAAc,SAAS,aAAa,CAAC;gCAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;gCACzC,MAAM,cAAc,SAAS,aAAa,CAAC;gCAE3C,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;oCAC7D,4DAA4D;oCAC5D,MAAM,YAAY,WAAW,MAAO,mBAAmB;oCACvD,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,kBAAkB,CAAC,EAAG,gBAAgB;oCAE9F,+BAA+B;oCAC/B,IAAI,aAAa;wCACf,MAAM,cAAc,WAAW,MAAO,mBAAmB;wCACzD,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,YAAY,MAAM,CAAC;oCACrE;oCAEA,+CAA+C;oCAC/C,MAAM,cAAc,SAAS,aAAa,CAAC;oCAC3C,IAAI,aAAa;wCACf,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,IAAK,WAAW,CAAC,OAAO,WAAW,GAAG,GAAG,IAAM,cAAc;wCAC1F,YAAY,KAAK,CAAC,OAAO,GAAG,SAAS,QAAQ;wCAC7C,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,IAAI,GAAG,CAAC,EAAG,mBAAmB;oCACvF;gCACF;gCACA,UAAU;4BACZ;;wBACA,UAAU;oBACZ;gBACF;;YAEA,yCAAyC;YACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;gBAAE,SAAS;YAAK;YAElE,mBAAmB;YACnB;8CAAO;oBACL,SAAS,UAAU;oBACnB,YAAY,OAAO;sDAAC,CAAA;4BAClB,KAAK,mBAAmB,CAAC,SAAS;wBACpC;;oBACA,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;qCAAG;QAAC;KAAiB;IAErB,OAAO,MAAM,yCAAyC;AACxD;GAjLwB;KAAA", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/EnhancedInteractions.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function EnhancedInteractions() {\n  // Cursor trail effect for desktop\n  const createCursorTrail = useCallback(() => {\n    if (typeof window === 'undefined' || window.innerWidth < 768) return;\n\n    const trail: HTMLElement[] = [];\n    const trailLength = 8;\n\n    // Create trail elements\n    for (let i = 0; i < trailLength; i++) {\n      const dot = document.createElement('div');\n      dot.className = 'cursor-trail';\n      dot.style.cssText = `\n        position: fixed;\n        width: ${6 - i * 0.5}px;\n        height: ${6 - i * 0.5}px;\n        background: rgba(51, 80, 184, ${0.8 - i * 0.1});\n        border-radius: 50%;\n        pointer-events: none;\n        z-index: 9999;\n        transition: transform 0.1s ease-out;\n        transform: translate(-50%, -50%);\n      `;\n      document.body.appendChild(dot);\n      trail.push(dot);\n    }\n\n    let mouseX = 0;\n    let mouseY = 0;\n    let currentX = 0;\n    let currentY = 0;\n\n    const updateTrail = () => {\n      currentX += (mouseX - currentX) * 0.1;\n      currentY += (mouseY - currentY) * 0.1;\n\n      trail.forEach((dot, index) => {\n        const delay = index * 0.02;\n        const x = currentX - (mouseX - currentX) * delay;\n        const y = currentY - (mouseY - currentY) * delay;\n        dot.style.left = `${x}px`;\n        dot.style.top = `${y}px`;\n      });\n\n      requestAnimationFrame(updateTrail);\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      mouseX = e.clientX;\n      mouseY = e.clientY;\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    updateTrail();\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      trail.forEach(dot => document.body.removeChild(dot));\n    };\n  }, []);\n\n  // Enhanced scroll progress indicator\n  const createScrollProgress = useCallback(() => {\n    const progressBar = document.createElement('div');\n    progressBar.className = 'scroll-progress';\n    progressBar.style.cssText = `\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 0%;\n      height: 3px;\n      background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));\n      z-index: 10000;\n      transition: width 0.1s ease-out;\n      box-shadow: 0 0 10px rgba(51, 80, 184, 0.5);\n    `;\n    document.body.appendChild(progressBar);\n\n    const updateProgress = () => {\n      const scrollTop = window.pageYOffset;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = (scrollTop / docHeight) * 100;\n      progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;\n    };\n\n    window.addEventListener('scroll', updateProgress, { passive: true });\n    updateProgress();\n\n    return () => {\n      window.removeEventListener('scroll', updateProgress);\n      if (progressBar.parentNode) {\n        progressBar.parentNode.removeChild(progressBar);\n      }\n    };\n  }, []);\n\n  // Magnetic hover effect for interactive elements\n  const createMagneticEffect = useCallback(() => {\n    const magneticElements = document.querySelectorAll('.email-link, .logo-item, .achievement-item');\n\n    magneticElements.forEach(element => {\n      const htmlElement = element as HTMLElement;\n      \n      const handleMouseMove = (e: MouseEvent) => {\n        const rect = htmlElement.getBoundingClientRect();\n        const x = e.clientX - rect.left - rect.width / 2;\n        const y = e.clientY - rect.top - rect.height / 2;\n        \n        const distance = Math.sqrt(x * x + y * y);\n        const maxDistance = 100;\n        \n        if (distance < maxDistance) {\n          const strength = (maxDistance - distance) / maxDistance;\n          const moveX = x * strength * 0.3;\n          const moveY = y * strength * 0.3;\n          \n          htmlElement.style.transform = `translate(${moveX}px, ${moveY}px)`;\n        }\n      };\n\n      const handleMouseLeave = () => {\n        htmlElement.style.transform = '';\n      };\n\n      htmlElement.addEventListener('mousemove', handleMouseMove);\n      htmlElement.addEventListener('mouseleave', handleMouseLeave);\n    });\n  }, []);\n\n  // Typing animation for hero text\n  const createTypingAnimation = useCallback(() => {\n    const heroTagline = document.querySelector('.hero-tagline') as HTMLElement;\n    if (!heroTagline) return;\n\n    const text = heroTagline.textContent || '';\n    heroTagline.textContent = '';\n    heroTagline.style.borderRight = '2px solid var(--color-copper)';\n    \n    let index = 0;\n    const typeSpeed = 100;\n    \n    const typeText = () => {\n      if (index < text.length) {\n        heroTagline.textContent += text.charAt(index);\n        index++;\n        setTimeout(typeText, typeSpeed);\n      } else {\n        // Remove cursor after typing is complete\n        setTimeout(() => {\n          heroTagline.style.borderRight = 'none';\n        }, 1000);\n      }\n    };\n\n    // Start typing after hero animations\n    setTimeout(typeText, 2000);\n  }, []);\n\n  // Removed floating shapes as requested\n\n  useEffect(() => {\n    const cleanupFunctions: (() => void)[] = [];\n\n    // Initialize all enhancements\n    const cursorCleanup = createCursorTrail();\n    const progressCleanup = createScrollProgress();\n    \n    // Delay these to ensure DOM is ready\n    setTimeout(() => {\n      createMagneticEffect();\n      createTypingAnimation();\n    }, 1000);\n\n    // Removed parallax shapes\n\n    if (cursorCleanup) cleanupFunctions.push(cursorCleanup);\n    if (progressCleanup) cleanupFunctions.push(progressCleanup);\n\n    return () => {\n      cleanupFunctions.forEach(cleanup => cleanup());\n    };\n  }, [createCursorTrail, createScrollProgress, createMagneticEffect, createTypingAnimation]);\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACpC,IAAI,aAAkB,eAAe,OAAO,UAAU,GAAG,KAAK;YAE9D,MAAM,QAAuB,EAAE;YAC/B,MAAM,cAAc;YAEpB,wBAAwB;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gBACpC,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,SAAS,GAAG;gBAChB,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC;;eAEZ,EAAE,IAAI,IAAI,IAAI;gBACb,EAAE,IAAI,IAAI,IAAI;sCACQ,EAAE,MAAM,IAAI,IAAI;;;;;;MAMhD,CAAC;gBACD,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,MAAM,IAAI,CAAC;YACb;YAEA,IAAI,SAAS;YACb,IAAI,SAAS;YACb,IAAI,WAAW;YACf,IAAI,WAAW;YAEf,MAAM;mFAAc;oBAClB,YAAY,CAAC,SAAS,QAAQ,IAAI;oBAClC,YAAY,CAAC,SAAS,QAAQ,IAAI;oBAElC,MAAM,OAAO;2FAAC,CAAC,KAAK;4BAClB,MAAM,QAAQ,QAAQ;4BACtB,MAAM,IAAI,WAAW,CAAC,SAAS,QAAQ,IAAI;4BAC3C,MAAM,IAAI,WAAW,CAAC,SAAS,QAAQ,IAAI;4BAC3C,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;4BACzB,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;wBAC1B;;oBAEA,sBAAsB;gBACxB;;YAEA,MAAM;uFAAkB,CAAC;oBACvB,SAAS,EAAE,OAAO;oBAClB,SAAS,EAAE,OAAO;gBACpB;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;YAEA;uEAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,MAAM,OAAO;+EAAC,CAAA,MAAO,SAAS,IAAI,CAAC,WAAW,CAAC;;gBACjD;;QACF;8DAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE;YACvC,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,YAAY,SAAS,GAAG;YACxB,YAAY,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;;;;;IAU7B,CAAC;YACD,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,MAAM;yFAAiB;oBACrB,MAAM,YAAY,OAAO,WAAW;oBACpC,MAAM,YAAY,SAAS,eAAe,CAAC,YAAY,GAAG,OAAO,WAAW;oBAC5E,MAAM,gBAAgB,AAAC,YAAY,YAAa;oBAChD,YAAY,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,eAAe,KAAK,CAAC,CAAC;gBAC9D;;YAEA,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;gBAAE,SAAS;YAAK;YAClE;YAEA;0EAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,YAAY,UAAU,EAAE;wBAC1B,YAAY,UAAU,CAAC,WAAW,CAAC;oBACrC;gBACF;;QACF;iEAAG,EAAE;IAEL,iDAAiD;IACjD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE;YACvC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YAEnD,iBAAiB,OAAO;0EAAC,CAAA;oBACvB,MAAM,cAAc;oBAEpB,MAAM;kGAAkB,CAAC;4BACvB,MAAM,OAAO,YAAY,qBAAqB;4BAC9C,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;4BAC/C,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;4BAE/C,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;4BACvC,MAAM,cAAc;4BAEpB,IAAI,WAAW,aAAa;gCAC1B,MAAM,WAAW,CAAC,cAAc,QAAQ,IAAI;gCAC5C,MAAM,QAAQ,IAAI,WAAW;gCAC7B,MAAM,QAAQ,IAAI,WAAW;gCAE7B,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC;4BACnE;wBACF;;oBAEA,MAAM;mGAAmB;4BACvB,YAAY,KAAK,CAAC,SAAS,GAAG;wBAChC;;oBAEA,YAAY,gBAAgB,CAAC,aAAa;oBAC1C,YAAY,gBAAgB,CAAC,cAAc;gBAC7C;;QACF;iEAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAAE;YACxC,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,IAAI,CAAC,aAAa;YAElB,MAAM,OAAO,YAAY,WAAW,IAAI;YACxC,YAAY,WAAW,GAAG;YAC1B,YAAY,KAAK,CAAC,WAAW,GAAG;YAEhC,IAAI,QAAQ;YACZ,MAAM,YAAY;YAElB,MAAM;oFAAW;oBACf,IAAI,QAAQ,KAAK,MAAM,EAAE;wBACvB,YAAY,WAAW,IAAI,KAAK,MAAM,CAAC;wBACvC;wBACA,WAAW,UAAU;oBACvB,OAAO;wBACL,yCAAyC;wBACzC;gGAAW;gCACT,YAAY,KAAK,CAAC,WAAW,GAAG;4BAClC;+FAAG;oBACL;gBACF;;YAEA,qCAAqC;YACrC,WAAW,UAAU;QACvB;kEAAG,EAAE;IAEL,uCAAuC;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,mBAAmC,EAAE;YAE3C,8BAA8B;YAC9B,MAAM,gBAAgB;YACtB,MAAM,kBAAkB;YAExB,qCAAqC;YACrC;kDAAW;oBACT;oBACA;gBACF;iDAAG;YAEH,0BAA0B;YAE1B,IAAI,eAAe,iBAAiB,IAAI,CAAC;YACzC,IAAI,iBAAiB,iBAAiB,IAAI,CAAC;YAE3C;kDAAO;oBACL,iBAAiB,OAAO;0DAAC,CAAA,UAAW;;gBACtC;;QACF;yCAAG;QAAC;QAAmB;QAAsB;QAAsB;KAAsB;IAEzF,OAAO;AACT;GAxLwB;KAAA", "debugId": null}}]}