(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ScrollAnimations.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ScrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function ScrollAnimations() {
    _s();
    // Performance-optimized observer callback
    const observerCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ScrollAnimations.useCallback[observerCallback]": (entries)=>{
            entries.forEach({
                "ScrollAnimations.useCallback[observerCallback]": (entry)=>{
                    const element = entry.target;
                    const intersectionRatio = entry.intersectionRatio;
                    if (entry.isIntersecting) {
                        // Add visible class for basic animations
                        element.classList.add('visible', 'revealed');
                        // Add progressive reveal based on intersection ratio
                        if (intersectionRatio > 0.5) {
                            element.classList.add('fully-visible');
                        }
                        // Trigger staggered animations for child elements
                        const children = element.querySelectorAll('.stagger-child');
                        children.forEach({
                            "ScrollAnimations.useCallback[observerCallback]": (child, index)=>{
                                setTimeout({
                                    "ScrollAnimations.useCallback[observerCallback]": ()=>{
                                        child.classList.add('revealed');
                                    }
                                }["ScrollAnimations.useCallback[observerCallback]"], index * 100);
                            }
                        }["ScrollAnimations.useCallback[observerCallback]"]);
                    } else {
                        // Optional: Remove classes for re-animation on scroll up
                        if (intersectionRatio === 0) {
                            element.classList.remove('fully-visible');
                        }
                    }
                }
            }["ScrollAnimations.useCallback[observerCallback]"]);
        }
    }["ScrollAnimations.useCallback[observerCallback]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ScrollAnimations.useEffect": ()=>{
            // Enhanced Intersection Observer for sophisticated animations
            const observerOptions = {
                threshold: [
                    0,
                    0.1,
                    0.25,
                    0.5,
                    0.75,
                    1
                ],
                rootMargin: '0px 0px -50px 0px'
            };
            const observer = new IntersectionObserver(observerCallback, observerOptions);
            // Enhanced section animations with different reveal types
            const sections = document.querySelectorAll('section');
            sections.forEach({
                "ScrollAnimations.useEffect": (section, index)=>{
                    const htmlSection = section;
                    // Alternate animation types for visual variety
                    const animationType = index % 3;
                    switch(animationType){
                        case 0:
                            htmlSection.classList.add('reveal');
                            break;
                        case 1:
                            htmlSection.classList.add('reveal-scale');
                            break;
                        case 2:
                            htmlSection.classList.add('reveal-slide-left');
                            break;
                    }
                    htmlSection.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(section);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced timeline items with staggered reveals
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-slide-right');
                    htmlItem.style.transitionDelay = `${index * 0.2}s`;
                    // Add stagger-child class to timeline content
                    const content = htmlItem.querySelector('.timeline-content');
                    if (content) {
                        content.classList.add('stagger-child');
                    }
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced interest items with hover effects
            const interestItems = document.querySelectorAll('.interest-item');
            interestItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal', 'hover-lift');
                    htmlItem.style.transitionDelay = `${index * 0.1}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced achievement items with scale animations
            const achievementItems = document.querySelectorAll('.achievement-item');
            achievementItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-scale', 'hover-lift');
                    htmlItem.style.transitionDelay = `${index * 0.15}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced logo items with glow effects
            const logoItems = document.querySelectorAll('.logo-item');
            logoItems.forEach({
                "ScrollAnimations.useEffect": (item, index)=>{
                    const htmlItem = item;
                    htmlItem.classList.add('reveal-scale', 'hover-glow');
                    htmlItem.style.transitionDelay = `${index * 0.2}s`;
                    observer.observe(item);
                }
            }["ScrollAnimations.useEffect"]);
            // Smooth scroll for any anchor links (future use)
            const handleSmoothScroll = {
                "ScrollAnimations.useEffect.handleSmoothScroll": (e)=>{
                    const target = e.target;
                    if (target.hash) {
                        e.preventDefault();
                        const element = document.querySelector(target.hash);
                        if (element) {
                            element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                }
            }["ScrollAnimations.useEffect.handleSmoothScroll"];
            // Add smooth scroll to all anchor links
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach({
                "ScrollAnimations.useEffect": (link)=>{
                    link.addEventListener('click', handleSmoothScroll);
                }
            }["ScrollAnimations.useEffect"]);
            // Enhanced parallax effect with multiple layers
            let ticking = false;
            const handleParallax = {
                "ScrollAnimations.useEffect.handleParallax": ()=>{
                    if (!ticking) {
                        requestAnimationFrame({
                            "ScrollAnimations.useEffect.handleParallax": ()=>{
                                const scrolled = window.pageYOffset;
                                const heroSection = document.querySelector('.hero-section');
                                const heroImage = document.querySelector('.hero-image');
                                const heroOverlay = document.querySelector('.hero-overlay');
                                if (heroSection && heroImage && scrolled < window.innerHeight) {
                                    // Reduced parallax for hero image (faster, more responsive)
                                    const imageRate = scrolled * 0.15; // Reduced from 0.3
                                    heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.05)`; // Reduced scale
                                    // Minimal parallax for overlay
                                    if (heroOverlay) {
                                        const overlayRate = scrolled * 0.05; // Reduced from 0.1
                                        heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;
                                    }
                                    // Faster fade out hero content as user scrolls
                                    const heroContent = document.querySelector('.hero-content');
                                    if (heroContent) {
                                        const fadeRate = Math.max(0, 1 - scrolled / (window.innerHeight * 0.5)); // Faster fade
                                        heroContent.style.opacity = fadeRate.toString();
                                        heroContent.style.transform = `translateY(${scrolled * 0.1}px)`; // Reduced movement
                                    }
                                }
                                ticking = false;
                            }
                        }["ScrollAnimations.useEffect.handleParallax"]);
                        ticking = true;
                    }
                }
            }["ScrollAnimations.useEffect.handleParallax"];
            // Add optimized parallax scroll listener
            window.addEventListener('scroll', handleParallax, {
                passive: true
            });
            // Cleanup function
            return ({
                "ScrollAnimations.useEffect": ()=>{
                    observer.disconnect();
                    anchorLinks.forEach({
                        "ScrollAnimations.useEffect": (link)=>{
                            link.removeEventListener('click', handleSmoothScroll);
                        }
                    }["ScrollAnimations.useEffect"]);
                    window.removeEventListener('scroll', handleParallax);
                }
            })["ScrollAnimations.useEffect"];
        }
    }["ScrollAnimations.useEffect"], [
        observerCallback
    ]);
    return null; // This component doesn't render anything
}
_s(ScrollAnimations, "itVl4HLVJritfmW2X9C+P71p/6s=");
_c = ScrollAnimations;
var _c;
__turbopack_context__.k.register(_c, "ScrollAnimations");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/EnhancedInteractions.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EnhancedInteractions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function EnhancedInteractions() {
    _s();
    // Cursor trail effect for desktop
    const createCursorTrail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EnhancedInteractions.useCallback[createCursorTrail]": ()=>{
            if ("object" === 'undefined' || window.innerWidth < 768) return;
            const trail = [];
            const trailLength = 8;
            // Create trail elements
            for(let i = 0; i < trailLength; i++){
                const dot = document.createElement('div');
                dot.className = 'cursor-trail';
                dot.style.cssText = `
        position: fixed;
        width: ${6 - i * 0.5}px;
        height: ${6 - i * 0.5}px;
        background: rgba(51, 80, 184, ${0.8 - i * 0.1});
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transition: transform 0.1s ease-out;
        transform: translate(-50%, -50%);
      `;
                document.body.appendChild(dot);
                trail.push(dot);
            }
            let mouseX = 0;
            let mouseY = 0;
            let currentX = 0;
            let currentY = 0;
            const updateTrail = {
                "EnhancedInteractions.useCallback[createCursorTrail].updateTrail": ()=>{
                    currentX += (mouseX - currentX) * 0.1;
                    currentY += (mouseY - currentY) * 0.1;
                    trail.forEach({
                        "EnhancedInteractions.useCallback[createCursorTrail].updateTrail": (dot, index)=>{
                            const delay = index * 0.02;
                            const x = currentX - (mouseX - currentX) * delay;
                            const y = currentY - (mouseY - currentY) * delay;
                            dot.style.left = `${x}px`;
                            dot.style.top = `${y}px`;
                        }
                    }["EnhancedInteractions.useCallback[createCursorTrail].updateTrail"]);
                    requestAnimationFrame(updateTrail);
                }
            }["EnhancedInteractions.useCallback[createCursorTrail].updateTrail"];
            const handleMouseMove = {
                "EnhancedInteractions.useCallback[createCursorTrail].handleMouseMove": (e)=>{
                    mouseX = e.clientX;
                    mouseY = e.clientY;
                }
            }["EnhancedInteractions.useCallback[createCursorTrail].handleMouseMove"];
            document.addEventListener('mousemove', handleMouseMove);
            updateTrail();
            return ({
                "EnhancedInteractions.useCallback[createCursorTrail]": ()=>{
                    document.removeEventListener('mousemove', handleMouseMove);
                    trail.forEach({
                        "EnhancedInteractions.useCallback[createCursorTrail]": (dot)=>document.body.removeChild(dot)
                    }["EnhancedInteractions.useCallback[createCursorTrail]"]);
                }
            })["EnhancedInteractions.useCallback[createCursorTrail]"];
        }
    }["EnhancedInteractions.useCallback[createCursorTrail]"], []);
    // Enhanced scroll progress indicator
    const createScrollProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EnhancedInteractions.useCallback[createScrollProgress]": ()=>{
            const progressBar = document.createElement('div');
            progressBar.className = 'scroll-progress';
            progressBar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 0%;
      height: 3px;
      background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
      z-index: 10000;
      transition: width 0.1s ease-out;
      box-shadow: 0 0 10px rgba(51, 80, 184, 0.5);
    `;
            document.body.appendChild(progressBar);
            const updateProgress = {
                "EnhancedInteractions.useCallback[createScrollProgress].updateProgress": ()=>{
                    const scrollTop = window.pageYOffset;
                    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                    const scrollPercent = scrollTop / docHeight * 100;
                    progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;
                }
            }["EnhancedInteractions.useCallback[createScrollProgress].updateProgress"];
            window.addEventListener('scroll', updateProgress, {
                passive: true
            });
            updateProgress();
            return ({
                "EnhancedInteractions.useCallback[createScrollProgress]": ()=>{
                    window.removeEventListener('scroll', updateProgress);
                    if (progressBar.parentNode) {
                        progressBar.parentNode.removeChild(progressBar);
                    }
                }
            })["EnhancedInteractions.useCallback[createScrollProgress]"];
        }
    }["EnhancedInteractions.useCallback[createScrollProgress]"], []);
    // Magnetic hover effect for interactive elements
    const createMagneticEffect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EnhancedInteractions.useCallback[createMagneticEffect]": ()=>{
            const magneticElements = document.querySelectorAll('.email-link, .logo-item, .achievement-item');
            magneticElements.forEach({
                "EnhancedInteractions.useCallback[createMagneticEffect]": (element)=>{
                    const htmlElement = element;
                    const handleMouseMove = {
                        "EnhancedInteractions.useCallback[createMagneticEffect].handleMouseMove": (e)=>{
                            const rect = htmlElement.getBoundingClientRect();
                            const x = e.clientX - rect.left - rect.width / 2;
                            const y = e.clientY - rect.top - rect.height / 2;
                            const distance = Math.sqrt(x * x + y * y);
                            const maxDistance = 100;
                            if (distance < maxDistance) {
                                const strength = (maxDistance - distance) / maxDistance;
                                const moveX = x * strength * 0.3;
                                const moveY = y * strength * 0.3;
                                htmlElement.style.transform = `translate(${moveX}px, ${moveY}px)`;
                            }
                        }
                    }["EnhancedInteractions.useCallback[createMagneticEffect].handleMouseMove"];
                    const handleMouseLeave = {
                        "EnhancedInteractions.useCallback[createMagneticEffect].handleMouseLeave": ()=>{
                            htmlElement.style.transform = '';
                        }
                    }["EnhancedInteractions.useCallback[createMagneticEffect].handleMouseLeave"];
                    htmlElement.addEventListener('mousemove', handleMouseMove);
                    htmlElement.addEventListener('mouseleave', handleMouseLeave);
                }
            }["EnhancedInteractions.useCallback[createMagneticEffect]"]);
        }
    }["EnhancedInteractions.useCallback[createMagneticEffect]"], []);
    // Typing animation for hero text
    const createTypingAnimation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EnhancedInteractions.useCallback[createTypingAnimation]": ()=>{
            const heroTagline = document.querySelector('.hero-tagline');
            if (!heroTagline) return;
            const text = heroTagline.textContent || '';
            heroTagline.textContent = '';
            heroTagline.style.borderRight = '2px solid var(--color-copper)';
            let index = 0;
            const typeSpeed = 100;
            const typeText = {
                "EnhancedInteractions.useCallback[createTypingAnimation].typeText": ()=>{
                    if (index < text.length) {
                        heroTagline.textContent += text.charAt(index);
                        index++;
                        setTimeout(typeText, typeSpeed);
                    } else {
                        // Remove cursor after typing is complete
                        setTimeout({
                            "EnhancedInteractions.useCallback[createTypingAnimation].typeText": ()=>{
                                heroTagline.style.borderRight = 'none';
                            }
                        }["EnhancedInteractions.useCallback[createTypingAnimation].typeText"], 1000);
                    }
                }
            }["EnhancedInteractions.useCallback[createTypingAnimation].typeText"];
            // Start typing after hero animations
            setTimeout(typeText, 2000);
        }
    }["EnhancedInteractions.useCallback[createTypingAnimation]"], []);
    // Removed floating shapes as requested
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EnhancedInteractions.useEffect": ()=>{
            const cleanupFunctions = [];
            // Initialize all enhancements
            const cursorCleanup = createCursorTrail();
            const progressCleanup = createScrollProgress();
            // Delay these to ensure DOM is ready
            setTimeout({
                "EnhancedInteractions.useEffect": ()=>{
                    createMagneticEffect();
                    createTypingAnimation();
                }
            }["EnhancedInteractions.useEffect"], 1000);
            // Removed parallax shapes
            if (cursorCleanup) cleanupFunctions.push(cursorCleanup);
            if (progressCleanup) cleanupFunctions.push(progressCleanup);
            return ({
                "EnhancedInteractions.useEffect": ()=>{
                    cleanupFunctions.forEach({
                        "EnhancedInteractions.useEffect": (cleanup)=>cleanup()
                    }["EnhancedInteractions.useEffect"]);
                }
            })["EnhancedInteractions.useEffect"];
        }
    }["EnhancedInteractions.useEffect"], [
        createCursorTrail,
        createScrollProgress,
        createMagneticEffect,
        createTypingAnimation
    ]);
    return null;
}
_s(EnhancedInteractions, "07qcaaIa6V8Nn5W6b4tIPFaPWiY=");
_c = EnhancedInteractions;
var _c;
__turbopack_context__.k.register(_c, "EnhancedInteractions");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_7123b500._.js.map