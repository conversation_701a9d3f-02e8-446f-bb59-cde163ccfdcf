{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"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\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/src/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,oxHAAoxH;AAE/yH,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}