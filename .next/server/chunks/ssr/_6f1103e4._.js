module.exports = {

"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
;
;
const metadata = {
    title: "Widmark Ramgoolie - Medical Student & Researcher",
    description: "Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.",
    keywords: "Widmark Ramgoolie, MBBS, medical student, UWI, Caribbean healthcare, medical research, Trinidad, St. Augustine",
    authors: [
        {
            name: "Widmark Ramgoolie"
        }
    ],
    creator: "Widmark Ramgoolie",
    publisher: "Widmark Ramgoolie",
    robots: "index, follow",
    openGraph: {
        type: "website",
        locale: "en_US",
        url: "https://widmarkramgoolie.com",
        title: "Widmark Ramgoolie - Medical Student & Researcher",
        description: "Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.",
        siteName: "Widmark Ramgoolie Portfolio",
        images: [
            {
                url: "/images/portrait-placeholder.svg",
                width: 800,
                height: 1200,
                alt: "Widmark Ramgoolie Portrait"
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        title: "Widmark Ramgoolie - Medical Student & Researcher",
        description: "Fourth-year MBBS student at UWI exploring global medicine through local practice.",
        images: [
            "/images/portrait-placeholder.svg"
        ]
    },
    alternates: {
        canonical: "https://widmarkramgoolie.com"
    }
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 48,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 47,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=_6f1103e4._.js.map