{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '100vh',\n      backgroundColor: '#ffffff'\n    }}>\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '3px solid #e5e5e5',\n          borderTop: '3px solid #b87333',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }}></div>\n      </div>\n      <p style={{\n        color: '#4a4a4a',\n        fontFamily: 'Inter, sans-serif',\n        fontSize: '1.125rem'\n      }}>Loading Widmark's Portfolio...</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,OAAO;YACV,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB;QACnB;;0BACE,8OAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;0BACjC,cAAA,8OAAC;oBAAI,OAAO;wBACV,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,cAAc;wBACd,WAAW;oBACb;;;;;;;;;;;0BAEF,8OAAC;gBAAE,OAAO;oBACR,OAAO;oBACP,YAAY;oBACZ,UAAU;gBACZ;0BAAG;;;;;;;;;;;;AAGT", "debugId": null}}]}