{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/HeroSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"hero-section\">\n      <div className=\"hero-background\">\n        <Image\n          src=\"/images/portrait-placeholder.svg\"\n          alt=\"Widmark Ramgoolie Portrait\"\n          fill\n          priority\n          className=\"hero-image\"\n        />\n        <div className=\"hero-overlay\" />\n      </div>\n      <div className=\"hero-content\">\n        <h1 className=\"hero-name\">Widmark Ramgoolie</h1>\n        <h2 className=\"hero-title\">Fourth-Year MBBS Student</h2>\n        <p className=\"hero-tagline\">Exploring Global Medicine Through Local Practice</p>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,QAAQ;wBACR,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,8OAAC;wBAAG,WAAU;kCAAa;;;;;;kCAC3B,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AboutSection.tsx"], "sourcesContent": ["export default function AboutSection() {\n  return (\n    <section className=\"about-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">About Widmark</h2>\n        <div className=\"about-content\">\n          <p>\n            <PERSON><PERSON><PERSON> is a fourth-year undergraduate student in the Faculty of Medical Sciences at the University of the West Indies, St. Augustine, where he is pursuing a Bachelor of Medicine and Bachelor of Surgery (MBBS).\n          </p>\n          <p>\n            Driven by a deep sense of purpose, <PERSON><PERSON><PERSON>'s interests lie in understanding how healthcare systems and medical practices vary across borders—regionally and globally. His long-term goal is to bridge these gaps through evidence-based medicine, academic inquiry, and empathetic clinical care.\n          </p>\n          <p>\n            Through this journey, <PERSON><PERSON><PERSON> combines a curiosity for science with a commitment to social health equity, aiming to leave a lasting impact on the future of Caribbean and international healthcare.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AcademicJourneySection.tsx"], "sourcesContent": ["export default function AcademicJourneySection() {\n  return (\n    <section className=\"academic-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Academic Journey</h2>\n        <div className=\"timeline\">\n          <div className=\"timeline-item\">\n            <div className=\"timeline-date\">2021 – Present</div>\n            <div className=\"timeline-content\">\n              <h3>MBBS, University of the West Indies, St. Augustine</h3>\n              <p>Faculty of Medical Sciences</p>\n            </div>\n          </div>\n          \n          <div className=\"timeline-item\">\n            <div className=\"timeline-date\">2019 – 2021</div>\n            <div className=\"timeline-content\">\n              <h3>CAPE – Biology, Chemistry, Physics, Caribbean Studies</h3>\n              <p>St. Joseph's College, Trinidad</p>\n            </div>\n          </div>\n          \n          <div className=\"timeline-item\">\n            <div className=\"timeline-date\">2014 – 2019</div>\n            <div className=\"timeline-content\">\n              <h3>CSEC – 10 Subjects with Distinctions including Human & Social Biology</h3>\n              <p><PERSON><PERSON> Joseph's Secondary School</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ResearchInterestsSection.tsx"], "sourcesContent": ["export default function ResearchInterestsSection() {\n  const interests = [\n    \"International and regional differences in medical practice\",\n    \"Clinical outcomes in resource-limited settings\",\n    \"Community health and health equity\",\n    \"Surgical techniques and procedural precision\",\n    \"Integration of technology in medical training\"\n  ];\n\n  return (\n    <section className=\"research-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Research & Clinical Interests</h2>\n        <ul className=\"interests-list\">\n          {interests.map((interest, index) => (\n            <li key={index} className=\"interest-item\">\n              {interest}\n            </li>\n          ))}\n        </ul>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AchievementsSection.tsx"], "sourcesContent": ["export default function AchievementsSection() {\n  const achievements = [\n    \"Dean's Honour Roll – UWI, Faculty of Medical Sciences (2023)\",\n    \"Lead Student Researcher, Study on Regional Diagnostic Outcomes (2023)\",\n    \"Top CAPE Performer in Biology & Chemistry – St. Joseph's College (2021)\",\n    \"Represented UWI at Pan-Caribbean Health Research Forum (2022)\"\n  ];\n\n  return (\n    <section className=\"achievements-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Achievements & Recognitions</h2>\n        <ul className=\"achievements-list\">\n          {achievements.map((achievement, index) => (\n            <li key={index} className=\"achievement-item\">\n              {achievement}\n            </li>\n          ))}\n        </ul>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAG,WAAU;8BACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AffiliationsSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function AffiliationsSection() {\n  const affiliations = [\n    {\n      name: \"University of the West Indies\",\n      logo: \"/images/uwi-logo-placeholder.svg\",\n      alt: \"UWI Logo\"\n    },\n    {\n      name: \"Caribbean Public Health Agency\",\n      logo: \"/images/carpha-logo-placeholder.svg\",\n      alt: \"CARPHA Logo\"\n    },\n    {\n      name: \"St. Joseph's College\",\n      logo: \"/images/stjosephs-logo-placeholder.svg\",\n      alt: \"St. Joseph's College Logo\"\n    }\n  ];\n\n  return (\n    <section className=\"affiliations-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Affiliations & Institutions</h2>\n        <p className=\"affiliations-subtitle\">Affiliated With:</p>\n        <div className=\"logos-grid\">\n          {affiliations.map((affiliation, index) => (\n            <div key={index} className=\"logo-item\">\n              <Image\n                src={affiliation.logo}\n                alt={affiliation.alt}\n                width={120}\n                height={60}\n                className=\"institution-logo\"\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,IAAI;gCACrB,KAAK,YAAY,GAAG;gCACpB,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;2BANJ;;;;;;;;;;;;;;;;;;;;;AActB", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/FutureAspirationsSection.tsx"], "sourcesContent": ["export default function FutureAspirationsSection() {\n  return (\n    <section className=\"aspirations-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Future Aspirations</h2>\n        <blockquote className=\"aspirations-quote\">\n          <p>\n            \"My vision is to serve as both a clinician and academic—delivering care in communities that need it most, while also contributing to research that informs smarter, more inclusive healthcare systems.\n          </p>\n          <p>\n            The Caribbean is rich in talent and resilience. I hope to elevate its medical standards by participating in global dialogues and bringing those lessons home.\"\n          </p>\n        </blockquote>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAW,WAAU;;sCACpB,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ContactSection.tsx"], "sourcesContent": ["export default function ContactSection() {\n  return (\n    <section className=\"contact-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Get In Touch</h2>\n        <p className=\"contact-description\">\n          Whether you're interested in collaboration, research, or just want to connect, you can reach me via email.\n        </p>\n        <div className=\"contact-info\">\n          <a href=\"mailto:<EMAIL>\" className=\"email-link\">\n            📧 <EMAIL>\n          </a>\n          <div className=\"social-links\">\n            <span className=\"social-placeholder\">[LinkedIn Icon]</span>\n            <span className=\"social-placeholder\">[ResearchGate Icon]</span>\n            <span className=\"social-note\">(optional)</span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAGnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,MAAK;4BAAqC,WAAU;sCAAa;;;;;;sCAGpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;8CACrC,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;8CACrC,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/page.tsx"], "sourcesContent": ["import HeroSection from '@/components/HeroSection';\nimport AboutSection from '@/components/AboutSection';\nimport AcademicJourneySection from '@/components/AcademicJourneySection';\nimport ResearchInterestsSection from '@/components/ResearchInterestsSection';\nimport AchievementsSection from '@/components/AchievementsSection';\nimport AffiliationsSection from '@/components/AffiliationsSection';\nimport FutureAspirationsSection from '@/components/FutureAspirationsSection';\nimport ContactSection from '@/components/ContactSection';\nimport ScrollAnimations from '@/components/ScrollAnimations';\n\nexport default function Home() {\n  return (\n    <main>\n      <HeroSection />\n      <AboutSection />\n      <AcademicJourneySection />\n      <ResearchInterestsSection />\n      <AchievementsSection />\n      <AffiliationsSection />\n      <FutureAspirationsSection />\n      <ContactSection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,kIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,4IAAA,CAAA,UAAsB;;;;;0BACvB,8OAAC,8IAAA,CAAA,UAAwB;;;;;0BACzB,8OAAC,yIAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,yIAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,8IAAA,CAAA,UAAwB;;;;;0BACzB,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;AAGrB", "debugId": null}}]}