module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/ScrollAnimations.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ScrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
function ScrollAnimations() {
    // Performance-optimized observer callback
    const observerCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entries)=>{
        entries.forEach((entry)=>{
            const element = entry.target;
            const intersectionRatio = entry.intersectionRatio;
            if (entry.isIntersecting) {
                // Add visible class for basic animations
                element.classList.add('visible', 'revealed');
                // Add progressive reveal based on intersection ratio
                if (intersectionRatio > 0.5) {
                    element.classList.add('fully-visible');
                }
                // Trigger staggered animations for child elements
                const children = element.querySelectorAll('.stagger-child');
                children.forEach((child, index)=>{
                    setTimeout(()=>{
                        child.classList.add('revealed');
                    }, index * 100);
                });
            } else {
                // Optional: Remove classes for re-animation on scroll up
                if (intersectionRatio === 0) {
                    element.classList.remove('fully-visible');
                }
            }
        });
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Enhanced Intersection Observer for sophisticated animations
        const observerOptions = {
            threshold: [
                0,
                0.1,
                0.25,
                0.5,
                0.75,
                1
            ],
            rootMargin: '0px 0px -50px 0px'
        };
        const observer = new IntersectionObserver(observerCallback, observerOptions);
        // Enhanced section animations with different reveal types
        const sections = document.querySelectorAll('section');
        sections.forEach((section, index)=>{
            const htmlSection = section;
            // Alternate animation types for visual variety
            const animationType = index % 3;
            switch(animationType){
                case 0:
                    htmlSection.classList.add('reveal');
                    break;
                case 1:
                    htmlSection.classList.add('reveal-scale');
                    break;
                case 2:
                    htmlSection.classList.add('reveal-slide-left');
                    break;
            }
            htmlSection.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(section);
        });
        // Enhanced timeline items with staggered reveals
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index)=>{
            const htmlItem = item;
            htmlItem.classList.add('reveal-slide-right');
            htmlItem.style.transitionDelay = `${index * 0.2}s`;
            // Add stagger-child class to timeline content
            const content = htmlItem.querySelector('.timeline-content');
            if (content) {
                content.classList.add('stagger-child');
            }
            observer.observe(item);
        });
        // Enhanced interest items with hover effects
        const interestItems = document.querySelectorAll('.interest-item');
        interestItems.forEach((item, index)=>{
            const htmlItem = item;
            htmlItem.classList.add('reveal', 'hover-lift');
            htmlItem.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(item);
        });
        // Enhanced achievement items with scale animations
        const achievementItems = document.querySelectorAll('.achievement-item');
        achievementItems.forEach((item, index)=>{
            const htmlItem = item;
            htmlItem.classList.add('reveal-scale', 'hover-lift');
            htmlItem.style.transitionDelay = `${index * 0.15}s`;
            observer.observe(item);
        });
        // Enhanced logo items with glow effects
        const logoItems = document.querySelectorAll('.logo-item');
        logoItems.forEach((item, index)=>{
            const htmlItem = item;
            htmlItem.classList.add('reveal-scale', 'hover-glow');
            htmlItem.style.transitionDelay = `${index * 0.2}s`;
            observer.observe(item);
        });
        // Smooth scroll for any anchor links (future use)
        const handleSmoothScroll = (e)=>{
            const target = e.target;
            if (target.hash) {
                e.preventDefault();
                const element = document.querySelector(target.hash);
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        };
        // Add smooth scroll to all anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach((link)=>{
            link.addEventListener('click', handleSmoothScroll);
        });
        // Enhanced parallax effect with multiple layers
        let ticking = false;
        const handleParallax = ()=>{
            if (!ticking) {
                requestAnimationFrame(()=>{
                    const scrolled = window.pageYOffset;
                    const heroSection = document.querySelector('.hero-section');
                    const heroImage = document.querySelector('.hero-image');
                    const heroOverlay = document.querySelector('.hero-overlay');
                    if (heroSection && heroImage && scrolled < window.innerHeight) {
                        // Parallax for hero image
                        const imageRate = scrolled * 0.3;
                        heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.1)`;
                        // Subtle parallax for overlay
                        if (heroOverlay) {
                            const overlayRate = scrolled * 0.1;
                            heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;
                        }
                        // Fade out hero content as user scrolls
                        const heroContent = document.querySelector('.hero-content');
                        if (heroContent) {
                            const fadeRate = Math.max(0, 1 - scrolled / (window.innerHeight * 0.8));
                            heroContent.style.opacity = fadeRate.toString();
                            heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;
                        }
                    }
                    ticking = false;
                });
                ticking = true;
            }
        };
        // Add optimized parallax scroll listener
        window.addEventListener('scroll', handleParallax, {
            passive: true
        });
        // Cleanup function
        return ()=>{
            observer.disconnect();
            anchorLinks.forEach((link)=>{
                link.removeEventListener('click', handleSmoothScroll);
            });
            window.removeEventListener('scroll', handleParallax);
        };
    }, [
        observerCallback
    ]);
    return null; // This component doesn't render anything
}
}}),
"[project]/src/components/EnhancedInteractions.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EnhancedInteractions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
function EnhancedInteractions() {
    // Cursor trail effect for desktop
    const createCursorTrail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if ("undefined" === 'undefined' || window.innerWidth < 768) return;
        "TURBOPACK unreachable";
        const trail = undefined;
        const trailLength = undefined;
        let i;
        let mouseX;
        let mouseY;
        let currentX;
        let currentY;
        const updateTrail = undefined;
        const handleMouseMove = undefined;
    }, []);
    // Enhanced scroll progress indicator
    const createScrollProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        progressBar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 0%;
      height: 3px;
      background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));
      z-index: 10000;
      transition: width 0.1s ease-out;
      box-shadow: 0 0 10px rgba(51, 80, 184, 0.5);
    `;
        document.body.appendChild(progressBar);
        const updateProgress = ()=>{
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = scrollTop / docHeight * 100;
            progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;
        };
        window.addEventListener('scroll', updateProgress, {
            passive: true
        });
        updateProgress();
        return ()=>{
            window.removeEventListener('scroll', updateProgress);
            if (progressBar.parentNode) {
                progressBar.parentNode.removeChild(progressBar);
            }
        };
    }, []);
    // Magnetic hover effect for interactive elements
    const createMagneticEffect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const magneticElements = document.querySelectorAll('.email-link, .logo-item, .achievement-item');
        magneticElements.forEach((element)=>{
            const htmlElement = element;
            const handleMouseMove = (e)=>{
                const rect = htmlElement.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                const distance = Math.sqrt(x * x + y * y);
                const maxDistance = 100;
                if (distance < maxDistance) {
                    const strength = (maxDistance - distance) / maxDistance;
                    const moveX = x * strength * 0.3;
                    const moveY = y * strength * 0.3;
                    htmlElement.style.transform = `translate(${moveX}px, ${moveY}px)`;
                }
            };
            const handleMouseLeave = ()=>{
                htmlElement.style.transform = '';
            };
            htmlElement.addEventListener('mousemove', handleMouseMove);
            htmlElement.addEventListener('mouseleave', handleMouseLeave);
        });
    }, []);
    // Typing animation for hero text
    const createTypingAnimation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const heroTagline = document.querySelector('.hero-tagline');
        if (!heroTagline) return;
        const text = heroTagline.textContent || '';
        heroTagline.textContent = '';
        heroTagline.style.borderRight = '2px solid var(--color-copper)';
        let index = 0;
        const typeSpeed = 100;
        const typeText = ()=>{
            if (index < text.length) {
                heroTagline.textContent += text.charAt(index);
                index++;
                setTimeout(typeText, typeSpeed);
            } else {
                // Remove cursor after typing is complete
                setTimeout(()=>{
                    heroTagline.style.borderRight = 'none';
                }, 1000);
            }
        };
        // Start typing after hero animations
        setTimeout(typeText, 2000);
    }, []);
    // Parallax effect for background elements
    const createParallaxElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        // Create floating geometric shapes
        const shapes = [
            'circle',
            'triangle',
            'square'
        ];
        const shapeElements = [];
        for(let i = 0; i < 6; i++){
            const shape = document.createElement('div');
            const shapeType = shapes[Math.floor(Math.random() * shapes.length)];
            shape.className = `floating-shape floating-${shapeType}`;
            shape.style.cssText = `
        position: fixed;
        width: ${20 + Math.random() * 40}px;
        height: ${20 + Math.random() * 40}px;
        background: rgba(51, 80, 184, ${0.05 + Math.random() * 0.1});
        border-radius: ${shapeType === 'circle' ? '50%' : '0'};
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
        pointer-events: none;
        z-index: 1;
        animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
        animation-delay: ${Math.random() * 2}s;
      `;
            if (shapeType === 'triangle') {
                shape.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
            }
            document.body.appendChild(shape);
            shapeElements.push(shape);
        }
        const handleScroll = ()=>{
            const scrollY = window.pageYOffset;
            shapeElements.forEach((shape, index)=>{
                const speed = 0.5 + index % 3 * 0.2;
                shape.style.transform = `translateY(${scrollY * speed}px)`;
            });
        };
        window.addEventListener('scroll', handleScroll, {
            passive: true
        });
        return ()=>{
            window.removeEventListener('scroll', handleScroll);
            shapeElements.forEach((shape)=>{
                if (shape.parentNode) {
                    shape.parentNode.removeChild(shape);
                }
            });
        };
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const cleanupFunctions = [];
        // Initialize all enhancements
        const cursorCleanup = createCursorTrail();
        const progressCleanup = createScrollProgress();
        // Delay these to ensure DOM is ready
        setTimeout(()=>{
            createMagneticEffect();
            createTypingAnimation();
        }, 1000);
        const parallaxCleanup = createParallaxElements();
        if (cursorCleanup) cleanupFunctions.push(cursorCleanup);
        if (progressCleanup) cleanupFunctions.push(progressCleanup);
        if (parallaxCleanup) cleanupFunctions.push(parallaxCleanup);
        return ()=>{
            cleanupFunctions.forEach((cleanup)=>cleanup());
        };
    }, [
        createCursorTrail,
        createScrollProgress,
        createMagneticEffect,
        createTypingAnimation,
        createParallaxElements
    ]);
    return null;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cb9caf14._.js.map