{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/HeroSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"hero-section\" aria-label=\"Hero section with Widmark Ramgoolie introduction\">\n      <div className=\"hero-background\">\n        <Image\n          src=\"/images/wid-drama.webp\"\n          alt=\"Professional portrait of <PERSON><PERSON><PERSON>, medical student at University of the West Indies\"\n          fill\n          priority\n          className=\"hero-image\"\n          sizes=\"100vw\"\n        />\n        <div className=\"hero-overlay\" aria-hidden=\"true\" />\n      </div>\n      <div className=\"hero-content\">\n        <h1 className=\"hero-name\">Widmark Ramgoolie</h1>\n        <h2 className=\"hero-title\">Fourth-Year MBBS Student</h2>\n        <p className=\"hero-tagline\">Exploring Global Medicine Through Local Practice</p>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAe,cAAW;;0BAC3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,QAAQ;wBACR,WAAU;wBACV,OAAM;;;;;;kCAER,8OAAC;wBAAI,WAAU;wBAAe,eAAY;;;;;;;;;;;;0BAE5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,8OAAC;wBAAG,WAAU;kCAAa;;;;;;kCAC3B,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AboutSection.tsx"], "sourcesContent": ["export default function AboutSection() {\n  return (\n    <section className=\"about-section\" aria-labelledby=\"about-heading\">\n      <div className=\"container\">\n        <h2 id=\"about-heading\" className=\"section-title\">About Widmark</h2>\n        <div className=\"about-content\">\n          <p>\n            <PERSON><PERSON><PERSON> is currently a fourth-year undergraduate medical student at the University of the West Indies, St. Augustine, pursuing a Bachelor of Medicine and Bachelor of Surgery (MBBS).\n          </p>\n          <p>\n            He has a strong interest in the global evolution of healthcare, particularly how regional medical systems and education practices differ and align. With a passion for academic inquiry and clinical advancement, <PERSON><PERSON><PERSON> is actively involved in student leadership and aims to contribute meaningfully to Caribbean medical excellence.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAgB,mBAAgB;kBACjD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,IAAG;oBAAgB,WAAU;8BAAgB;;;;;;8BACjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AcademicJourneySection.tsx"], "sourcesContent": ["export default function AcademicJourneySection() {\n  return (\n    <section className=\"academic-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Academic Journey</h2>\n        <div className=\"timeline\">\n          <div className=\"timeline-item\">\n            <div className=\"timeline-date\">2021 – Present</div>\n            <div className=\"timeline-content\">\n              <h3>MBBS, University of the West Indies, St. Augustine</h3>\n              <p>Faculty of Medical Sciences</p>\n            </div>\n          </div>\n\n          <div className=\"timeline-item\">\n            <div className=\"timeline-date\">2014 – 2021</div>\n            <div className=\"timeline-content\">\n              <h3>CAPE & CSEC Studies</h3>\n              <p>St. Joseph's Convent – St. Joseph</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"academic-achievement\">\n          <h3>Notable Achievement</h3>\n          <p>• Top Caribbean Performer in CAPE Biology (2020)</p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAKT,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ResearchInterestsSection.tsx"], "sourcesContent": ["export default function ResearchInterestsSection() {\n  const interests = [\n    \"International and regional differences in medical practice\",\n    \"Clinical outcomes in resource-limited settings\",\n    \"Community health and health equity\",\n    \"Surgical techniques and procedural precision\",\n    \"Integration of technology in medical training\"\n  ];\n\n  return (\n    <section className=\"research-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Research & Clinical Interests</h2>\n        <ul className=\"interests-list\">\n          {interests.map((interest, index) => (\n            <li key={index} className=\"interest-item\">\n              {interest}\n            </li>\n          ))}\n        </ul>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AchievementsSection.tsx"], "sourcesContent": ["export default function AchievementsSection() {\n  const achievements = [\n    \"Top Caribbean Performer in CAPE Biology (2020)\",\n    \"Appointed Member, MSSC Auxiliary Executive (2024)\",\n    \"Chairperson, Student Activities Committee – Faculty of Medical Sciences (2024)\"\n  ];\n\n  return (\n    <section className=\"achievements-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Achievements & Recognitions</h2>\n        <ul className=\"achievements-list\">\n          {achievements.map((achievement, index) => (\n            <li key={index} className=\"achievement-item\">\n              {achievement}\n            </li>\n          ))}\n        </ul>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAG,WAAU;8BACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/AffiliationsSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function AffiliationsSection() {\n  const affiliations = [\n    {\n      name: \"University of the West Indies\",\n      logo: \"/images/Coat_of_arms_of_the_University_of_the_West_Indies.png\",\n      alt: \"UWI Logo\"\n    },\n    {\n      name: \"St. Joseph's Convent – St. Joseph\",\n      logo: \"/images/301792561_567682651723271_2581192033815023727_n.jpg\",\n      alt: \"St. Joseph's Convent Logo\"\n    }\n  ];\n\n  return (\n    <section className=\"affiliations-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Affiliations & Institutions</h2>\n        <p className=\"affiliations-subtitle\">Affiliated With:</p>\n        <div className=\"logos-grid\">\n          {affiliations.map((affiliation, index) => (\n            <div key={index} className=\"logo-item\">\n              <Image\n                src={affiliation.logo}\n                alt={affiliation.alt}\n                width={160}\n                height={200}\n                className=\"institution-logo\"\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,IAAI;gCACrB,KAAK,YAAY,GAAG;gCACpB,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;2BANJ;;;;;;;;;;;;;;;;;;;;;AActB", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/FutureAspirationsSection.tsx"], "sourcesContent": ["export default function FutureAspirationsSection() {\n  return (\n    <section className=\"aspirations-section\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Future Aspirations</h2>\n        <blockquote className=\"aspirations-quote\">\n          <p>\n            \"My vision is to serve as both a clinician and academic—delivering care in communities that need it most, while also contributing to research that informs smarter, more inclusive healthcare systems.\n          </p>\n          <p>\n            The Caribbean is rich in talent and resilience. I hope to elevate its medical standards by participating in global dialogues and bringing those lessons home.\"\n          </p>\n        </blockquote>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAW,WAAU;;sCACpB,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ContactSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function ContactSection() {\n  return (\n    <section className=\"contact-section\" aria-labelledby=\"contact-heading\">\n      <div className=\"container\">\n        <h2 id=\"contact-heading\" className=\"section-title\">Get In Touch</h2>\n        <p className=\"contact-description\">\n          Whether you're interested in collaboration, research, or just want to connect, you can reach me via email.\n        </p>\n        <div className=\"contact-info\">\n          <a\n            href=\"mailto:<EMAIL>\"\n            className=\"email-link\"\n            aria-label=\"Send email to Widmark Ramgoolie\"\n          >\n            <span aria-hidden=\"true\">📧</span> <EMAIL>\n          </a>\n          <div className=\"social-links\" role=\"list\">\n            <span className=\"social-placeholder\" role=\"listitem\" aria-label=\"LinkedIn profile coming soon\">\n              <Image src=\"/images/linkedin.png\" alt=\"LinkedIn Icon\" width={24} height={24} />\n            </span>\n\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAkB,mBAAgB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,IAAG;oBAAkB,WAAU;8BAAgB;;;;;;8BACnD,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAGnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC;oCAAK,eAAY;8CAAO;;;;;;gCAAS;;;;;;;sCAEpC,8OAAC;4BAAI,WAAU;4BAAe,MAAK;sCACjC,cAAA,8OAAC;gCAAK,WAAU;gCAAqB,MAAK;gCAAW,cAAW;0CAC9D,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAI;oCAAuB,KAAI;oCAAgB,OAAO;oCAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvF", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollAnimations.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollAnimations.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/page.tsx"], "sourcesContent": ["import HeroSection from '@/components/HeroSection';\nimport AboutSection from '@/components/AboutSection';\nimport AcademicJourneySection from '@/components/AcademicJourneySection';\nimport ResearchInterestsSection from '@/components/ResearchInterestsSection';\nimport AchievementsSection from '@/components/AchievementsSection';\nimport AffiliationsSection from '@/components/AffiliationsSection';\nimport FutureAspirationsSection from '@/components/FutureAspirationsSection';\nimport ContactSection from '@/components/ContactSection';\nimport ScrollAnimations from '@/components/ScrollAnimations';\nimport EnhancedInteractions from '@/components/EnhancedInteractions';\n\nexport default function Home() {\n  return (\n    <main>\n      <ScrollAnimations />\n      <HeroSection />\n      <AboutSection />\n      <AcademicJourneySection />\n      <ResearchInterestsSection />\n      <AchievementsSection />\n      <AffiliationsSection />\n      <FutureAspirationsSection />\n      <ContactSection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,sIAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,kIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,4IAAA,CAAA,UAAsB;;;;;0BACvB,8OAAC,8IAAA,CAAA,UAAwB;;;;;0BACzB,8OAAC,yIAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,yIAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,8IAAA,CAAA,UAAwB;;;;;0BACzB,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;AAGrB", "debugId": null}}]}