{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport default function LoadingScreen() {\n  const [isLoading, setIsLoading] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    // Simulate loading progress\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setTimeout(() => setIsLoading(false), 500);\n          return 100;\n        }\n        return prev + Math.random() * 15;\n      });\n    }, 100);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (!isLoading) return null;\n\n  return (\n    <div className=\"loading-screen\">\n      <div className=\"loading-content\">\n        <div className=\"loading-logo\">\n          <div className=\"logo-circle\">\n            <div className=\"logo-inner\">WR</div>\n          </div>\n        </div>\n\n        <div className=\"loading-text\">\n          <h2>Widmark Ramgoolie</h2>\n          <p>Loading Portfolio...</p>\n        </div>\n\n        <div className=\"loading-progress\">\n          <div className=\"progress-bar\">\n            <div\n              className=\"progress-fill\"\n              style={{ width: `${Math.min(progress, 100)}%` }}\n            />\n          </div>\n          <span className=\"progress-text\">{Math.round(progress)}%</span>\n        </div>\n      </div>\n\n      <div className=\"loading-particles\">\n        {Array.from({ length: 20 }).map((_, i) => (\n          <div key={i} className={`particle particle-${i}`} />\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,MAAM,WAAW,YAAY;YAC3B,YAAY,CAAA;gBACV,IAAI,QAAQ,KAAK;oBACf,cAAc;oBACd,WAAW,IAAM,aAAa,QAAQ;oBACtC,OAAO;gBACT;gBACA,OAAO,OAAO,KAAK,MAAM,KAAK;YAChC;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;;;;;kCAIhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGlD,8OAAC;gCAAK,WAAU;;oCAAiB,KAAK,KAAK,CAAC;oCAAU;;;;;;;;;;;;;;;;;;;0BAI1D,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;wBAAY,WAAW,CAAC,kBAAkB,EAAE,GAAG;uBAAtC;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}