module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/ScrollAnimations.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ScrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
function ScrollAnimations() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        const observer = new IntersectionObserver((entries)=>{
            entries.forEach((entry)=>{
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);
        // Add fade-in class to all sections and observe them
        const sections = document.querySelectorAll('section');
        sections.forEach((section, index)=>{
            section.classList.add('fade-in');
            section.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(section);
        });
        // Add fade-in to timeline items
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index)=>{
            item.classList.add('fade-in');
            item.style.transitionDelay = `${index * 0.2}s`;
            observer.observe(item);
        });
        // Add fade-in to interest items
        const interestItems = document.querySelectorAll('.interest-item');
        interestItems.forEach((item, index)=>{
            item.classList.add('fade-in');
            item.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(item);
        });
        // Add fade-in to achievement items
        const achievementItems = document.querySelectorAll('.achievement-item');
        achievementItems.forEach((item, index)=>{
            item.classList.add('fade-in');
            item.style.transitionDelay = `${index * 0.15}s`;
            observer.observe(item);
        });
        // Add fade-in to logo items
        const logoItems = document.querySelectorAll('.logo-item');
        logoItems.forEach((item, index)=>{
            item.classList.add('fade-in');
            item.style.transitionDelay = `${index * 0.2}s`;
            observer.observe(item);
        });
        // Smooth scroll for any anchor links (future use)
        const handleSmoothScroll = (e)=>{
            const target = e.target;
            if (target.hash) {
                e.preventDefault();
                const element = document.querySelector(target.hash);
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        };
        // Add smooth scroll to all anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach((link)=>{
            link.addEventListener('click', handleSmoothScroll);
        });
        // Optimized parallax effect for hero section
        let ticking = false;
        const handleParallax = ()=>{
            if (!ticking) {
                requestAnimationFrame(()=>{
                    const scrolled = window.pageYOffset;
                    const heroSection = document.querySelector('.hero-section');
                    const heroImage = document.querySelector('.hero-image');
                    if (heroSection && heroImage && scrolled < window.innerHeight) {
                        const rate = scrolled * 0.3;
                        heroImage.style.transform = `translate3d(0, ${rate}px, 0)`;
                    }
                    ticking = false;
                });
                ticking = true;
            }
        };
        // Add optimized parallax scroll listener
        window.addEventListener('scroll', handleParallax, {
            passive: true
        });
        // Cleanup function
        return ()=>{
            observer.disconnect();
            anchorLinks.forEach((link)=>{
                link.removeEventListener('click', handleSmoothScroll);
            });
            window.removeEventListener('scroll', handleParallax);
        };
    }, []);
    return null; // This component doesn't render anything
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ad419ccb._.js.map