{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport \"../styles/components.css\";\nimport StructuredData from \"@/components/StructuredData\";\n\nexport const metadata: Metadata = {\n  title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n  description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.\",\n  keywords: \"Widmark Ramgoolie, MBBS, medical student, UWI, Caribbean healthcare, medical research, Trinidad, St. Augustine\",\n  authors: [{ name: \"Widmark Ramgoolie\" }],\n  creator: \"Widmark Ramgoolie\",\n  publisher: \"Widmark Ramgoolie\",\n  robots: \"index, follow\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://widmarkramgoolie.com\",\n    title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n    description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.\",\n    siteName: \"Widmark Ramgoolie Portfolio\",\n    images: [\n      {\n        url: \"/images/portrait-placeholder.svg\",\n        width: 800,\n        height: 1200,\n        alt: \"Widmark Ramgoolie Portrait\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n    description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice.\",\n    images: [\"/images/portrait-placeholder.svg\"],\n  },\n  alternates: {\n    canonical: \"https://widmarkramgoolie.com\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAKO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAoB;KAAE;IACxC,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAmC;IAC9C;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}