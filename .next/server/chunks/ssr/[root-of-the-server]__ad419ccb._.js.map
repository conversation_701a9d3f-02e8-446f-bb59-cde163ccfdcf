{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function ScrollAnimations() {\n  useEffect(() => {\n    // Intersection Observer for fade-in animations\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    // Add fade-in class to all sections and observe them\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      section.classList.add('fade-in');\n      section.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Add fade-in to timeline items\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to interest items\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to achievement items\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Add fade-in to logo items\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      item.classList.add('fade-in');\n      item.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Optimized parallax effect for hero section\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            const rate = scrolled * 0.3;\n            heroImage.style.transform = `translate3d(0, ${rate}px, 0)`;\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,MAAM,kBAAkB;YACtB,WAAW;YACX,YAAY;QACd;QAEA,MAAM,WAAW,IAAI,qBAAqB,CAAC;YACzC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;gBAC7B;YACF;QACF,GAAG;QAEH,qDAAqD;QACrD,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAC3C,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,QAAQ,SAAS,CAAC,GAAG,CAAC;YACtB,QAAQ,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YACjD,SAAS,OAAO,CAAC;QACnB;QAEA,gCAAgC;QAChC,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAC9C,SAAS,OAAO,CAAC;QACnB;QAEA,gCAAgC;QAChC,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAC9C,SAAS,OAAO,CAAC;QACnB;QAEA,mCAAmC;QACnC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;QACnD,iBAAiB,OAAO,CAAC,CAAC,MAAM;YAC9B,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;YAC/C,SAAS,OAAO,CAAC;QACnB;QAEA,4BAA4B;QAC5B,MAAM,YAAY,SAAS,gBAAgB,CAAC;QAC5C,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,KAAK,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAC9C,SAAS,OAAO,CAAC;QACnB;QAEA,kDAAkD;QAClD,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,IAAI,EAAE;gBACf,EAAE,cAAc;gBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;gBAClD,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBACrB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;QAC9C,YAAY,OAAO,CAAC,CAAA;YAClB,KAAK,gBAAgB,CAAC,SAAS;QACjC;QAEA,6CAA6C;QAC7C,IAAI,UAAU;QACd,MAAM,iBAAiB;YACrB,IAAI,CAAC,SAAS;gBACZ,sBAAsB;oBACpB,MAAM,WAAW,OAAO,WAAW;oBACnC,MAAM,cAAc,SAAS,aAAa,CAAC;oBAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;oBAEzC,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;wBAC7D,MAAM,OAAO,WAAW;wBACxB,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,KAAK,MAAM,CAAC;oBAC5D;oBACA,UAAU;gBACZ;gBACA,UAAU;YACZ;QACF;QAEA,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;YAAE,SAAS;QAAK;QAElE,mBAAmB;QACnB,OAAO;YACL,SAAS,UAAU;YACnB,YAAY,OAAO,CAAC,CAAA;gBAClB,KAAK,mBAAmB,CAAC,SAAS;YACpC;YACA,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD", "debugId": null}}]}