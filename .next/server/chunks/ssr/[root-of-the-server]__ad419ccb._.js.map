{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function ScrollAnimations() {\n  // Performance-optimized observer callback\n  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {\n    entries.forEach((entry) => {\n      const element = entry.target as HTMLElement;\n      const intersectionRatio = entry.intersectionRatio;\n\n      if (entry.isIntersecting) {\n        // Add visible class for basic animations\n        element.classList.add('visible', 'revealed');\n\n        // Add progressive reveal based on intersection ratio\n        if (intersectionRatio > 0.5) {\n          element.classList.add('fully-visible');\n        }\n\n        // Trigger staggered animations for child elements\n        const children = element.querySelectorAll('.stagger-child');\n        children.forEach((child, index) => {\n          setTimeout(() => {\n            (child as HTMLElement).classList.add('revealed');\n          }, index * 100);\n        });\n      } else {\n        // Optional: Remove classes for re-animation on scroll up\n        if (intersectionRatio === 0) {\n          element.classList.remove('fully-visible');\n        }\n      }\n    });\n  }, []);\n\n  useEffect(() => {\n    // Enhanced Intersection Observer for sophisticated animations\n    const observerOptions = {\n      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver(observerCallback, observerOptions);\n\n    // Enhanced section animations with different reveal types\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      const htmlSection = section as HTMLElement;\n\n      // Alternate animation types for visual variety\n      const animationType = index % 3;\n      switch (animationType) {\n        case 0:\n          htmlSection.classList.add('reveal');\n          break;\n        case 1:\n          htmlSection.classList.add('reveal-scale');\n          break;\n        case 2:\n          htmlSection.classList.add('reveal-slide-left');\n          break;\n      }\n\n      htmlSection.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Enhanced timeline items with staggered reveals\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-slide-right');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n\n      // Add stagger-child class to timeline content\n      const content = htmlItem.querySelector('.timeline-content');\n      if (content) {\n        content.classList.add('stagger-child');\n      }\n\n      observer.observe(item);\n    });\n\n    // Enhanced interest items with hover effects\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced achievement items with scale animations\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced logo items with glow effects\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-glow');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Enhanced parallax effect with multiple layers\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n          const heroOverlay = document.querySelector('.hero-overlay') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            // Parallax for hero image\n            const imageRate = scrolled * 0.3;\n            heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.1)`;\n\n            // Subtle parallax for overlay\n            if (heroOverlay) {\n              const overlayRate = scrolled * 0.1;\n              heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;\n            }\n\n            // Fade out hero content as user scrolls\n            const heroContent = document.querySelector('.hero-content') as HTMLElement;\n            if (heroContent) {\n              const fadeRate = Math.max(0, 1 - (scrolled / (window.innerHeight * 0.8)));\n              heroContent.style.opacity = fadeRate.toString();\n              heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;\n            }\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, [observerCallback]);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,QAAQ,OAAO,CAAC,CAAC;YACf,MAAM,UAAU,MAAM,MAAM;YAC5B,MAAM,oBAAoB,MAAM,iBAAiB;YAEjD,IAAI,MAAM,cAAc,EAAE;gBACxB,yCAAyC;gBACzC,QAAQ,SAAS,CAAC,GAAG,CAAC,WAAW;gBAEjC,qDAAqD;gBACrD,IAAI,oBAAoB,KAAK;oBAC3B,QAAQ,SAAS,CAAC,GAAG,CAAC;gBACxB;gBAEA,kDAAkD;gBAClD,MAAM,WAAW,QAAQ,gBAAgB,CAAC;gBAC1C,SAAS,OAAO,CAAC,CAAC,OAAO;oBACvB,WAAW;wBACR,MAAsB,SAAS,CAAC,GAAG,CAAC;oBACvC,GAAG,QAAQ;gBACb;YACF,OAAO;gBACL,yDAAyD;gBACzD,IAAI,sBAAsB,GAAG;oBAC3B,QAAQ,SAAS,CAAC,MAAM,CAAC;gBAC3B;YACF;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8DAA8D;QAC9D,MAAM,kBAAkB;YACtB,WAAW;gBAAC;gBAAG;gBAAK;gBAAM;gBAAK;gBAAM;aAAE;YACvC,YAAY;QACd;QAEA,MAAM,WAAW,IAAI,qBAAqB,kBAAkB;QAE5D,0DAA0D;QAC1D,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAC3C,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,MAAM,cAAc;YAEpB,+CAA+C;YAC/C,MAAM,gBAAgB,QAAQ;YAC9B,OAAQ;gBACN,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;YACJ;YAEA,YAAY,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YACrD,SAAS,OAAO,CAAC;QACnB;QAEA,iDAAiD;QACjD,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC;YACvB,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAElD,8CAA8C;YAC9C,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;YAEA,SAAS,OAAO,CAAC;QACnB;QAEA,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,UAAU;YACjC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAClD,SAAS,OAAO,CAAC;QACnB;QAEA,mDAAmD;QACnD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;QACnD,iBAAiB,OAAO,CAAC,CAAC,MAAM;YAC9B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;YACnD,SAAS,OAAO,CAAC;QACnB;QAEA,wCAAwC;QACxC,MAAM,YAAY,SAAS,gBAAgB,CAAC;QAC5C,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAClD,SAAS,OAAO,CAAC;QACnB;QAEA,kDAAkD;QAClD,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,IAAI,EAAE;gBACf,EAAE,cAAc;gBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;gBAClD,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBACrB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;QAC9C,YAAY,OAAO,CAAC,CAAA;YAClB,KAAK,gBAAgB,CAAC,SAAS;QACjC;QAEA,gDAAgD;QAChD,IAAI,UAAU;QACd,MAAM,iBAAiB;YACrB,IAAI,CAAC,SAAS;gBACZ,sBAAsB;oBACpB,MAAM,WAAW,OAAO,WAAW;oBACnC,MAAM,cAAc,SAAS,aAAa,CAAC;oBAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;oBACzC,MAAM,cAAc,SAAS,aAAa,CAAC;oBAE3C,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;wBAC7D,0BAA0B;wBAC1B,MAAM,YAAY,WAAW;wBAC7B,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,iBAAiB,CAAC;wBAE1E,8BAA8B;wBAC9B,IAAI,aAAa;4BACf,MAAM,cAAc,WAAW;4BAC/B,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,YAAY,MAAM,CAAC;wBACrE;wBAEA,wCAAwC;wBACxC,MAAM,cAAc,SAAS,aAAa,CAAC;wBAC3C,IAAI,aAAa;4BACf,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,IAAK,WAAW,CAAC,OAAO,WAAW,GAAG,GAAG;4BACtE,YAAY,KAAK,CAAC,OAAO,GAAG,SAAS,QAAQ;4BAC7C,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,IAAI,GAAG,CAAC;wBACjE;oBACF;oBACA,UAAU;gBACZ;gBACA,UAAU;YACZ;QACF;QAEA,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;YAAE,SAAS;QAAK;QAElE,mBAAmB;QACnB,OAAO;YACL,SAAS,UAAU;YACnB,YAAY,OAAO,CAAC,CAAA;gBAClB,KAAK,mBAAmB,CAAC,SAAS;YACpC;YACA,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO,MAAM,yCAAyC;AACxD", "debugId": null}}]}