{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function ScrollAnimations() {\n  // Performance-optimized observer callback\n  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {\n    entries.forEach((entry) => {\n      const element = entry.target as HTMLElement;\n      const intersectionRatio = entry.intersectionRatio;\n\n      if (entry.isIntersecting) {\n        // Add visible class for basic animations\n        element.classList.add('visible', 'revealed');\n\n        // Add progressive reveal based on intersection ratio\n        if (intersectionRatio > 0.5) {\n          element.classList.add('fully-visible');\n        }\n\n        // Trigger staggered animations for child elements\n        const children = element.querySelectorAll('.stagger-child');\n        children.forEach((child, index) => {\n          setTimeout(() => {\n            (child as HTMLElement).classList.add('revealed');\n          }, index * 100);\n        });\n      } else {\n        // Optional: Remove classes for re-animation on scroll up\n        if (intersectionRatio === 0) {\n          element.classList.remove('fully-visible');\n        }\n      }\n    });\n  }, []);\n\n  useEffect(() => {\n    // Enhanced Intersection Observer for sophisticated animations\n    const observerOptions = {\n      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver(observerCallback, observerOptions);\n\n    // Enhanced section animations with different reveal types\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section, index) => {\n      const htmlSection = section as HTMLElement;\n\n      // Alternate animation types for visual variety\n      const animationType = index % 3;\n      switch (animationType) {\n        case 0:\n          htmlSection.classList.add('reveal');\n          break;\n        case 1:\n          htmlSection.classList.add('reveal-scale');\n          break;\n        case 2:\n          htmlSection.classList.add('reveal-slide-left');\n          break;\n      }\n\n      htmlSection.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(section);\n    });\n\n    // Enhanced timeline items with staggered reveals\n    const timelineItems = document.querySelectorAll('.timeline-item');\n    timelineItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-slide-right');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n\n      // Add stagger-child class to timeline content\n      const content = htmlItem.querySelector('.timeline-content');\n      if (content) {\n        content.classList.add('stagger-child');\n      }\n\n      observer.observe(item);\n    });\n\n    // Enhanced interest items with hover effects\n    const interestItems = document.querySelectorAll('.interest-item');\n    interestItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.1}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced achievement items with scale animations\n    const achievementItems = document.querySelectorAll('.achievement-item');\n    achievementItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-lift');\n      htmlItem.style.transitionDelay = `${index * 0.15}s`;\n      observer.observe(item);\n    });\n\n    // Enhanced logo items with glow effects\n    const logoItems = document.querySelectorAll('.logo-item');\n    logoItems.forEach((item, index) => {\n      const htmlItem = item as HTMLElement;\n      htmlItem.classList.add('reveal-scale', 'hover-glow');\n      htmlItem.style.transitionDelay = `${index * 0.2}s`;\n      observer.observe(item);\n    });\n\n    // Smooth scroll for any anchor links (future use)\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add smooth scroll to all anchor links\n    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n    anchorLinks.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Enhanced parallax effect with multiple layers\n    let ticking = false;\n    const handleParallax = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          const scrolled = window.pageYOffset;\n          const heroSection = document.querySelector('.hero-section') as HTMLElement;\n          const heroImage = document.querySelector('.hero-image') as HTMLElement;\n          const heroOverlay = document.querySelector('.hero-overlay') as HTMLElement;\n\n          if (heroSection && heroImage && scrolled < window.innerHeight) {\n            // Parallax for hero image\n            const imageRate = scrolled * 0.3;\n            heroImage.style.transform = `translate3d(0, ${imageRate}px, 0) scale(1.1)`;\n\n            // Subtle parallax for overlay\n            if (heroOverlay) {\n              const overlayRate = scrolled * 0.1;\n              heroOverlay.style.transform = `translate3d(0, ${overlayRate}px, 0)`;\n            }\n\n            // Fade out hero content as user scrolls\n            const heroContent = document.querySelector('.hero-content') as HTMLElement;\n            if (heroContent) {\n              const fadeRate = Math.max(0, 1 - (scrolled / (window.innerHeight * 0.8)));\n              heroContent.style.opacity = fadeRate.toString();\n              heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;\n            }\n          }\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    // Add optimized parallax scroll listener\n    window.addEventListener('scroll', handleParallax, { passive: true });\n\n    // Cleanup function\n    return () => {\n      observer.disconnect();\n      anchorLinks.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      window.removeEventListener('scroll', handleParallax);\n    };\n  }, [observerCallback]);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,QAAQ,OAAO,CAAC,CAAC;YACf,MAAM,UAAU,MAAM,MAAM;YAC5B,MAAM,oBAAoB,MAAM,iBAAiB;YAEjD,IAAI,MAAM,cAAc,EAAE;gBACxB,yCAAyC;gBACzC,QAAQ,SAAS,CAAC,GAAG,CAAC,WAAW;gBAEjC,qDAAqD;gBACrD,IAAI,oBAAoB,KAAK;oBAC3B,QAAQ,SAAS,CAAC,GAAG,CAAC;gBACxB;gBAEA,kDAAkD;gBAClD,MAAM,WAAW,QAAQ,gBAAgB,CAAC;gBAC1C,SAAS,OAAO,CAAC,CAAC,OAAO;oBACvB,WAAW;wBACR,MAAsB,SAAS,CAAC,GAAG,CAAC;oBACvC,GAAG,QAAQ;gBACb;YACF,OAAO;gBACL,yDAAyD;gBACzD,IAAI,sBAAsB,GAAG;oBAC3B,QAAQ,SAAS,CAAC,MAAM,CAAC;gBAC3B;YACF;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8DAA8D;QAC9D,MAAM,kBAAkB;YACtB,WAAW;gBAAC;gBAAG;gBAAK;gBAAM;gBAAK;gBAAM;aAAE;YACvC,YAAY;QACd;QAEA,MAAM,WAAW,IAAI,qBAAqB,kBAAkB;QAE5D,0DAA0D;QAC1D,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAC3C,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,MAAM,cAAc;YAEpB,+CAA+C;YAC/C,MAAM,gBAAgB,QAAQ;YAC9B,OAAQ;gBACN,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,YAAY,SAAS,CAAC,GAAG,CAAC;oBAC1B;YACJ;YAEA,YAAY,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YACrD,SAAS,OAAO,CAAC;QACnB;QAEA,iDAAiD;QACjD,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC;YACvB,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAElD,8CAA8C;YAC9C,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;YAEA,SAAS,OAAO,CAAC;QACnB;QAEA,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;QAChD,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,UAAU;YACjC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAClD,SAAS,OAAO,CAAC;QACnB;QAEA,mDAAmD;QACnD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;QACnD,iBAAiB,OAAO,CAAC,CAAC,MAAM;YAC9B,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;YACnD,SAAS,OAAO,CAAC;QACnB;QAEA,wCAAwC;QACxC,MAAM,YAAY,SAAS,gBAAgB,CAAC;QAC5C,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,WAAW;YACjB,SAAS,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACvC,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC;YAClD,SAAS,OAAO,CAAC;QACnB;QAEA,kDAAkD;QAClD,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,IAAI,EAAE;gBACf,EAAE,cAAc;gBAChB,MAAM,UAAU,SAAS,aAAa,CAAC,OAAO,IAAI;gBAClD,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBACrB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,cAAc,SAAS,gBAAgB,CAAC;QAC9C,YAAY,OAAO,CAAC,CAAA;YAClB,KAAK,gBAAgB,CAAC,SAAS;QACjC;QAEA,gDAAgD;QAChD,IAAI,UAAU;QACd,MAAM,iBAAiB;YACrB,IAAI,CAAC,SAAS;gBACZ,sBAAsB;oBACpB,MAAM,WAAW,OAAO,WAAW;oBACnC,MAAM,cAAc,SAAS,aAAa,CAAC;oBAC3C,MAAM,YAAY,SAAS,aAAa,CAAC;oBACzC,MAAM,cAAc,SAAS,aAAa,CAAC;oBAE3C,IAAI,eAAe,aAAa,WAAW,OAAO,WAAW,EAAE;wBAC7D,0BAA0B;wBAC1B,MAAM,YAAY,WAAW;wBAC7B,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,iBAAiB,CAAC;wBAE1E,8BAA8B;wBAC9B,IAAI,aAAa;4BACf,MAAM,cAAc,WAAW;4BAC/B,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,YAAY,MAAM,CAAC;wBACrE;wBAEA,wCAAwC;wBACxC,MAAM,cAAc,SAAS,aAAa,CAAC;wBAC3C,IAAI,aAAa;4BACf,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,IAAK,WAAW,CAAC,OAAO,WAAW,GAAG,GAAG;4BACtE,YAAY,KAAK,CAAC,OAAO,GAAG,SAAS,QAAQ;4BAC7C,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,IAAI,GAAG,CAAC;wBACjE;oBACF;oBACA,UAAU;gBACZ;gBACA,UAAU;YACZ;QACF;QAEA,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;YAAE,SAAS;QAAK;QAElE,mBAAmB;QACnB,OAAO;YACL,SAAS,UAAU;YACnB,YAAY,OAAO,CAAC,CAAA;gBAClB,KAAK,mBAAmB,CAAC,SAAS;YACpC;YACA,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO,MAAM,yCAAyC;AACxD", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/EnhancedInteractions.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useCallback } from 'react';\n\nexport default function EnhancedInteractions() {\n  // Cursor trail effect for desktop\n  const createCursorTrail = useCallback(() => {\n    if (typeof window === 'undefined' || window.innerWidth < 768) return;\n\n    const trail: HTMLElement[] = [];\n    const trailLength = 8;\n\n    // Create trail elements\n    for (let i = 0; i < trailLength; i++) {\n      const dot = document.createElement('div');\n      dot.className = 'cursor-trail';\n      dot.style.cssText = `\n        position: fixed;\n        width: ${6 - i * 0.5}px;\n        height: ${6 - i * 0.5}px;\n        background: rgba(51, 80, 184, ${0.8 - i * 0.1});\n        border-radius: 50%;\n        pointer-events: none;\n        z-index: 9999;\n        transition: transform 0.1s ease-out;\n        transform: translate(-50%, -50%);\n      `;\n      document.body.appendChild(dot);\n      trail.push(dot);\n    }\n\n    let mouseX = 0;\n    let mouseY = 0;\n    let currentX = 0;\n    let currentY = 0;\n\n    const updateTrail = () => {\n      currentX += (mouseX - currentX) * 0.1;\n      currentY += (mouseY - currentY) * 0.1;\n\n      trail.forEach((dot, index) => {\n        const delay = index * 0.02;\n        const x = currentX - (mouseX - currentX) * delay;\n        const y = currentY - (mouseY - currentY) * delay;\n        dot.style.left = `${x}px`;\n        dot.style.top = `${y}px`;\n      });\n\n      requestAnimationFrame(updateTrail);\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      mouseX = e.clientX;\n      mouseY = e.clientY;\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    updateTrail();\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      trail.forEach(dot => document.body.removeChild(dot));\n    };\n  }, []);\n\n  // Enhanced scroll progress indicator\n  const createScrollProgress = useCallback(() => {\n    const progressBar = document.createElement('div');\n    progressBar.className = 'scroll-progress';\n    progressBar.style.cssText = `\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 0%;\n      height: 3px;\n      background: linear-gradient(90deg, var(--color-copper), var(--color-copper-dark));\n      z-index: 10000;\n      transition: width 0.1s ease-out;\n      box-shadow: 0 0 10px rgba(51, 80, 184, 0.5);\n    `;\n    document.body.appendChild(progressBar);\n\n    const updateProgress = () => {\n      const scrollTop = window.pageYOffset;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = (scrollTop / docHeight) * 100;\n      progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;\n    };\n\n    window.addEventListener('scroll', updateProgress, { passive: true });\n    updateProgress();\n\n    return () => {\n      window.removeEventListener('scroll', updateProgress);\n      if (progressBar.parentNode) {\n        progressBar.parentNode.removeChild(progressBar);\n      }\n    };\n  }, []);\n\n  // Magnetic hover effect for interactive elements\n  const createMagneticEffect = useCallback(() => {\n    const magneticElements = document.querySelectorAll('.email-link, .logo-item, .achievement-item');\n\n    magneticElements.forEach(element => {\n      const htmlElement = element as HTMLElement;\n      \n      const handleMouseMove = (e: MouseEvent) => {\n        const rect = htmlElement.getBoundingClientRect();\n        const x = e.clientX - rect.left - rect.width / 2;\n        const y = e.clientY - rect.top - rect.height / 2;\n        \n        const distance = Math.sqrt(x * x + y * y);\n        const maxDistance = 100;\n        \n        if (distance < maxDistance) {\n          const strength = (maxDistance - distance) / maxDistance;\n          const moveX = x * strength * 0.3;\n          const moveY = y * strength * 0.3;\n          \n          htmlElement.style.transform = `translate(${moveX}px, ${moveY}px)`;\n        }\n      };\n\n      const handleMouseLeave = () => {\n        htmlElement.style.transform = '';\n      };\n\n      htmlElement.addEventListener('mousemove', handleMouseMove);\n      htmlElement.addEventListener('mouseleave', handleMouseLeave);\n    });\n  }, []);\n\n  // Typing animation for hero text\n  const createTypingAnimation = useCallback(() => {\n    const heroTagline = document.querySelector('.hero-tagline') as HTMLElement;\n    if (!heroTagline) return;\n\n    const text = heroTagline.textContent || '';\n    heroTagline.textContent = '';\n    heroTagline.style.borderRight = '2px solid var(--color-copper)';\n    \n    let index = 0;\n    const typeSpeed = 100;\n    \n    const typeText = () => {\n      if (index < text.length) {\n        heroTagline.textContent += text.charAt(index);\n        index++;\n        setTimeout(typeText, typeSpeed);\n      } else {\n        // Remove cursor after typing is complete\n        setTimeout(() => {\n          heroTagline.style.borderRight = 'none';\n        }, 1000);\n      }\n    };\n\n    // Start typing after hero animations\n    setTimeout(typeText, 2000);\n  }, []);\n\n  // Parallax effect for background elements\n  const createParallaxElements = useCallback(() => {\n    // Create floating geometric shapes\n    const shapes = ['circle', 'triangle', 'square'];\n    const shapeElements: HTMLElement[] = [];\n\n    for (let i = 0; i < 6; i++) {\n      const shape = document.createElement('div');\n      const shapeType = shapes[Math.floor(Math.random() * shapes.length)];\n      \n      shape.className = `floating-shape floating-${shapeType}`;\n      shape.style.cssText = `\n        position: fixed;\n        width: ${20 + Math.random() * 40}px;\n        height: ${20 + Math.random() * 40}px;\n        background: rgba(51, 80, 184, ${0.05 + Math.random() * 0.1});\n        border-radius: ${shapeType === 'circle' ? '50%' : '0'};\n        left: ${Math.random() * 100}%;\n        top: ${Math.random() * 100}%;\n        pointer-events: none;\n        z-index: 1;\n        animation: float ${3 + Math.random() * 4}s ease-in-out infinite;\n        animation-delay: ${Math.random() * 2}s;\n      `;\n      \n      if (shapeType === 'triangle') {\n        shape.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';\n      }\n      \n      document.body.appendChild(shape);\n      shapeElements.push(shape);\n    }\n\n    const handleScroll = () => {\n      const scrollY = window.pageYOffset;\n      shapeElements.forEach((shape, index) => {\n        const speed = 0.5 + (index % 3) * 0.2;\n        shape.style.transform = `translateY(${scrollY * speed}px)`;\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      shapeElements.forEach(shape => {\n        if (shape.parentNode) {\n          shape.parentNode.removeChild(shape);\n        }\n      });\n    };\n  }, []);\n\n  useEffect(() => {\n    const cleanupFunctions: (() => void)[] = [];\n\n    // Initialize all enhancements\n    const cursorCleanup = createCursorTrail();\n    const progressCleanup = createScrollProgress();\n    \n    // Delay these to ensure DOM is ready\n    setTimeout(() => {\n      createMagneticEffect();\n      createTypingAnimation();\n    }, 1000);\n\n    const parallaxCleanup = createParallaxElements();\n\n    if (cursorCleanup) cleanupFunctions.push(cursorCleanup);\n    if (progressCleanup) cleanupFunctions.push(progressCleanup);\n    if (parallaxCleanup) cleanupFunctions.push(parallaxCleanup);\n\n    return () => {\n      cleanupFunctions.forEach(cleanup => cleanup());\n    };\n  }, [createCursorTrail, createScrollProgress, createMagneticEffect, createTypingAnimation, createParallaxElements]);\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,gBAAkB,eAAe,OAAO,UAAU,GAAG,KAAK;;QAE9D,MAAM;QACN,MAAM;QAGD,IAAI;QAkBT,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,MAAM;QAeN,MAAM;IAYR,GAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,YAAY,SAAS,GAAG;QACxB,YAAY,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;;;;;IAU7B,CAAC;QACD,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,MAAM,iBAAiB;YACrB,MAAM,YAAY,OAAO,WAAW;YACpC,MAAM,YAAY,SAAS,eAAe,CAAC,YAAY,GAAG,OAAO,WAAW;YAC5E,MAAM,gBAAgB,AAAC,YAAY,YAAa;YAChD,YAAY,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,eAAe,KAAK,CAAC,CAAC;QAC9D;QAEA,OAAO,gBAAgB,CAAC,UAAU,gBAAgB;YAAE,SAAS;QAAK;QAClE;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,YAAY,UAAU,EAAE;gBAC1B,YAAY,UAAU,CAAC,WAAW,CAAC;YACrC;QACF;IACF,GAAG,EAAE;IAEL,iDAAiD;IACjD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;QAEnD,iBAAiB,OAAO,CAAC,CAAA;YACvB,MAAM,cAAc;YAEpB,MAAM,kBAAkB,CAAC;gBACvB,MAAM,OAAO,YAAY,qBAAqB;gBAC9C,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBAC/C,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;gBAE/C,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;gBACvC,MAAM,cAAc;gBAEpB,IAAI,WAAW,aAAa;oBAC1B,MAAM,WAAW,CAAC,cAAc,QAAQ,IAAI;oBAC5C,MAAM,QAAQ,IAAI,WAAW;oBAC7B,MAAM,QAAQ,IAAI,WAAW;oBAE7B,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC;gBACnE;YACF;YAEA,MAAM,mBAAmB;gBACvB,YAAY,KAAK,CAAC,SAAS,GAAG;YAChC;YAEA,YAAY,gBAAgB,CAAC,aAAa;YAC1C,YAAY,gBAAgB,CAAC,cAAc;QAC7C;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,IAAI,CAAC,aAAa;QAElB,MAAM,OAAO,YAAY,WAAW,IAAI;QACxC,YAAY,WAAW,GAAG;QAC1B,YAAY,KAAK,CAAC,WAAW,GAAG;QAEhC,IAAI,QAAQ;QACZ,MAAM,YAAY;QAElB,MAAM,WAAW;YACf,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,YAAY,WAAW,IAAI,KAAK,MAAM,CAAC;gBACvC;gBACA,WAAW,UAAU;YACvB,OAAO;gBACL,yCAAyC;gBACzC,WAAW;oBACT,YAAY,KAAK,CAAC,WAAW,GAAG;gBAClC,GAAG;YACL;QACF;QAEA,qCAAqC;QACrC,WAAW,UAAU;IACvB,GAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,mCAAmC;QACnC,MAAM,SAAS;YAAC;YAAU;YAAY;SAAS;QAC/C,MAAM,gBAA+B,EAAE;QAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,YAAY,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;YAEnE,MAAM,SAAS,GAAG,CAAC,wBAAwB,EAAE,WAAW;YACxD,MAAM,KAAK,CAAC,OAAO,GAAG,CAAC;;eAEd,EAAE,KAAK,KAAK,MAAM,KAAK,GAAG;gBACzB,EAAE,KAAK,KAAK,MAAM,KAAK,GAAG;sCACJ,EAAE,OAAO,KAAK,MAAM,KAAK,IAAI;uBAC5C,EAAE,cAAc,WAAW,QAAQ,IAAI;cAChD,EAAE,KAAK,MAAM,KAAK,IAAI;aACvB,EAAE,KAAK,MAAM,KAAK,IAAI;;;yBAGV,EAAE,IAAI,KAAK,MAAM,KAAK,EAAE;yBACxB,EAAE,KAAK,MAAM,KAAK,EAAE;MACvC,CAAC;YAED,IAAI,cAAc,YAAY;gBAC5B,MAAM,KAAK,CAAC,QAAQ,GAAG;YACzB;YAEA,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,cAAc,IAAI,CAAC;QACrB;QAEA,MAAM,eAAe;YACnB,MAAM,UAAU,OAAO,WAAW;YAClC,cAAc,OAAO,CAAC,CAAC,OAAO;gBAC5B,MAAM,QAAQ,MAAM,AAAC,QAAQ,IAAK;gBAClC,MAAM,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,MAAM,GAAG,CAAC;YAC5D;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAEhE,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,cAAc,OAAO,CAAC,CAAA;gBACpB,IAAI,MAAM,UAAU,EAAE;oBACpB,MAAM,UAAU,CAAC,WAAW,CAAC;gBAC/B;YACF;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmC,EAAE;QAE3C,8BAA8B;QAC9B,MAAM,gBAAgB;QACtB,MAAM,kBAAkB;QAExB,qCAAqC;QACrC,WAAW;YACT;YACA;QACF,GAAG;QAEH,MAAM,kBAAkB;QAExB,IAAI,eAAe,iBAAiB,IAAI,CAAC;QACzC,IAAI,iBAAiB,iBAAiB,IAAI,CAAC;QAC3C,IAAI,iBAAiB,iBAAiB,IAAI,CAAC;QAE3C,OAAO;YACL,iBAAiB,OAAO,CAAC,CAAA,UAAW;QACtC;IACF,GAAG;QAAC;QAAmB;QAAsB;QAAsB;QAAuB;KAAuB;IAEjH,OAAO;AACT", "debugId": null}}]}