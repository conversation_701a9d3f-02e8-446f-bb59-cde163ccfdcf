{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/components/StructuredData.tsx"], "sourcesContent": ["export default function StructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Person\",\n    \"name\": \"Widmark Ramgoolie\",\n    \"jobTitle\": \"Medical Student\",\n    \"description\": \"Fourth-year MBBS student at UWI exploring global medicine through local practice\",\n    \"url\": \"https://widmarkramgoolie.com\",\n    \"image\": \"https://widmarkramgoolie.com/images/portrait-placeholder.svg\",\n    \"sameAs\": [\n      \"https://linkedin.com/in/widmark-ramgoolie\",\n      \"https://researchgate.net/profile/widmark-ramgoolie\"\n    ],\n    \"alumniOf\": [\n      {\n        \"@type\": \"EducationalOrganization\",\n        \"name\": \"St. Joseph's Convent – St. Joseph\"\n      }\n    ],\n    \"studiesAt\": {\n      \"@type\": \"EducationalOrganization\",\n      \"name\": \"University of the West Indies, St. Augustine\",\n      \"department\": \"Faculty of Medical Sciences\"\n    },\n    \"knowsAbout\": [\n      \"Medicine\",\n      \"Healthcare Systems\",\n      \"Medical Research\", \n      \"Community Health\",\n      \"Health Equity\",\n      \"Caribbean Healthcare\",\n      \"Evidence-based Medicine\"\n    ],\n    \"award\": [\n      \"Top Caribbean Performer in CAPE Biology (2020)\",\n      \"Appointed Member, MSSC Auxiliary Executive (2024)\",\n      \"Chairperson, Student Activities Committee – Faculty of Medical Sciences (2024)\"\n    ],\n    \"memberOf\": [\n      {\n        \"@type\": \"Organization\",\n        \"name\": \"University of the West Indies\"\n      },\n      {\n        \"@type\": \"Organization\",\n        \"name\": \"MSSC Auxiliary Executive\"\n      }\n    ],\n    \"nationality\": \"Trinidad and Tobago\",\n    \"address\": {\n      \"@type\": \"PostalAddress\",\n      \"addressCountry\": \"Trinidad and Tobago\"\n    },\n    \"email\": \"<EMAIL>\"\n  };\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,UAAU;YACR;YACA;SACD;QACD,YAAY;YACV;gBACE,SAAS;gBACT,QAAQ;YACV;SACD;QACD,aAAa;YACX,SAAS;YACT,QAAQ;YACR,cAAc;QAChB;QACA,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;QACD,YAAY;YACV;gBACE,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,SAAS;gBACT,QAAQ;YACV;SACD;QACD,eAAe;QACf,WAAW;YACT,SAAS;YACT,kBAAkB;QACpB;QACA,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport \"../styles/components.css\";\nimport StructuredData from \"@/components/StructuredData\";\n\nexport const metadata: Metadata = {\n  metadataBase: new URL('https://widmarkramgoolie.com'),\n  title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n  description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.\",\n  keywords: \"Widmark Ramgoolie, MBBS, medical student, UWI, Caribbean healthcare, medical research, Trinidad, St. Augustine\",\n  authors: [{ name: \"Widmark Ramgoolie\" }],\n  creator: \"Widmark Ramgoolie\",\n  publisher: \"Widmark Ramgoolie\",\n  robots: \"index, follow\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://widmarkramgoolie.com\",\n    title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n    description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice. Passionate about healthcare equity and evidence-based medicine.\",\n    siteName: \"Widmark Ramgoolie Portfolio\",\n    images: [\n      {\n        url: \"/images/portrait-placeholder.svg\",\n        width: 800,\n        height: 1200,\n        alt: \"Widmark Ramgoolie Portrait\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Widmark Ramgoolie - Medical Student & Researcher\",\n    description: \"Fourth-year MBBS student at UWI exploring global medicine through local practice.\",\n    images: [\"/images/portrait-placeholder.svg\"],\n  },\n  alternates: {\n    canonical: \"https://widmarkramgoolie.com\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link\n          href=\"https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&display=swap\"\n          rel=\"stylesheet\"\n        />\n        <StructuredData />\n      </head>\n      <body>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;;;;;AAEO,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAoB;KAAE;IACxC,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAmC;IAC9C;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBACC,MAAK;wBACL,KAAI;;;;;;kCAEN,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;0BAEjB,8OAAC;0BACE;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Widmark/biography/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}